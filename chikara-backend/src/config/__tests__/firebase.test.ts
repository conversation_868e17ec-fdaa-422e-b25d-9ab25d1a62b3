/**
 * Tests for Firebase configuration
 */
import { describe, it, expect, beforeEach, afterEach, vi } from "vitest";
import {
    initializeFirebase,
    getFirebaseMessaging,
    isFirebaseInitialized,
    sendPushNotification,
    sendPushNotifications,
    NotificationPayload,
} from "../firebase";
import { FirebaseMockHelper, TestDataFactory } from "../test/testUtils";

// Mock Firebase Admin SDK
const mockFirebaseAdmin = {
    initializeApp: vi.fn(),
    cert: vi.fn(),
};

const mockMessaging = FirebaseMockHelper.createMockMessaging();

vi.mock("firebase-admin/app", () => ({
    initializeApp: mockFirebaseAdmin.initializeApp,
    cert: mockFirebaseAdmin.cert,
}));

vi.mock("firebase-admin/messaging", () => ({
    getMessaging: vi.fn(() => mockMessaging),
}));

// Mock logger
vi.mock("../utils/log.js", () => ({
    logger: {
        info: vi.fn(),
        error: vi.fn(),
        debug: vi.fn(),
        warn: vi.fn(),
    },
}));

describe("Firebase Configuration", () => {
    const originalEnv = process.env;

    beforeEach(() => {
        vi.clearAllMocks();

        // Reset environment variables
        process.env = {
            ...originalEnv,
            FIREBASE_ENABLED: "true",
            FIREBASE_PROJECT_ID: "test-project",
            FIREBASE_CLIENT_EMAIL: "<EMAIL>",
            FIREBASE_PRIVATE_KEY: "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----\n",
            APP_BASE_URL: "https://test.example.com",
        };

        // Reset module state
        (global as any).__firebaseApp = null;
    });

    afterEach(() => {
        process.env = originalEnv;
    });

    describe("initializeFirebase", () => {
        it("should initialize Firebase successfully with valid configuration", async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);
            mockFirebaseAdmin.cert.mockReturnValue({});

            await initializeFirebase();

            expect(mockFirebaseAdmin.cert).toHaveBeenCalledWith({
                projectId: "test-project",
                clientEmail: "<EMAIL>",
                privateKey: "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----\n",
            });
            expect(mockFirebaseAdmin.initializeApp).toHaveBeenCalledWith({
                credential: {},
            });
        });

        it("should skip initialization when Firebase is disabled", async () => {
            process.env.FIREBASE_ENABLED = "false";

            await initializeFirebase();

            expect(mockFirebaseAdmin.initializeApp).not.toHaveBeenCalled();
        });

        it("should handle missing private key", async () => {
            delete process.env.FIREBASE_PRIVATE_KEY;

            await expect(initializeFirebase()).rejects.toThrow("FIREBASE_PRIVATE_KEY environment variable is required");
        });

        it("should handle Firebase initialization errors", async () => {
            mockFirebaseAdmin.initializeApp.mockImplementation(() => {
                throw new Error("Firebase initialization failed");
            });

            await expect(initializeFirebase()).rejects.toThrow("Firebase initialization failed");
        });

        it("should not initialize twice", async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);

            await initializeFirebase();
            await initializeFirebase();

            expect(mockFirebaseAdmin.initializeApp).toHaveBeenCalledTimes(1);
        });

        it("should use default values for missing environment variables", async () => {
            delete process.env.FIREBASE_PROJECT_ID;
            delete process.env.FIREBASE_CLIENT_EMAIL;

            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);

            await initializeFirebase();

            expect(mockFirebaseAdmin.cert).toHaveBeenCalledWith({
                projectId: "chikara-academy",
                clientEmail: "<EMAIL>",
                privateKey: "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----\n",
            });
        });

        it("should handle newline replacement in private key", async () => {
            process.env.FIREBASE_PRIVATE_KEY = "-----BEGIN PRIVATE KEY-----\\ntest-key\\n-----END PRIVATE KEY-----\\n";

            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);

            await initializeFirebase();

            expect(mockFirebaseAdmin.cert).toHaveBeenCalledWith(
                expect.objectContaining({
                    privateKey: "-----BEGIN PRIVATE KEY-----\ntest-key\n-----END PRIVATE KEY-----\n",
                })
            );
        });
    });

    describe("getFirebaseMessaging", () => {
        it("should return messaging instance when Firebase is initialized", async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);

            await initializeFirebase();
            const messaging = getFirebaseMessaging();

            expect(messaging).toBeDefined();
            expect(messaging).toBe(mockMessaging);
        });

        it("should throw error when Firebase is not initialized", () => {
            expect(() => getFirebaseMessaging()).toThrow("Firebase not initialized. Call initializeFirebase() first.");
        });
    });

    describe("isFirebaseInitialized", () => {
        it("should return false initially", () => {
            expect(isFirebaseInitialized()).toBe(false);
        });

        it("should return false when Firebase is disabled", async () => {
            process.env.FIREBASE_ENABLED = "false";
            await initializeFirebase();
            expect(isFirebaseInitialized()).toBe(false);
        });

        it("should return true after successful initialization", async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);

            await initializeFirebase();
            expect(isFirebaseInitialized()).toBe(true);
        });
    });

    describe("sendPushNotification", () => {
        beforeEach(async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);
            await initializeFirebase();
        });

        it("should send push notification successfully", async () => {
            const payload: NotificationPayload = {
                title: "Test Notification",
                body: "Test message",
                data: { key: "value" },
            };

            mockMessaging.send.mockResolvedValue("message-id-123");

            const result = await sendPushNotification(1, "test-token", payload);

            expect(result).toBe("message-id-123");
            expect(mockMessaging.send).toHaveBeenCalledWith({
                token: "test-token",
                notification: {
                    title: "Test Notification",
                    body: "Test message",
                    imageUrl: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                },
                webpush: {
                    headers: {
                        image: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                    },
                    fcmOptions: {
                        link: "https://test.example.com",
                    },
                },
                android: {
                    notification: {
                        imageUrl: "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
                    },
                },
                data: { key: "value" },
            });
        });

        it("should use default title when not provided", async () => {
            const payload: NotificationPayload = {
                body: "Test message",
            };

            mockMessaging.send.mockResolvedValue("message-id-123");

            await sendPushNotification(1, "test-token", payload);

            expect(mockMessaging.send).toHaveBeenCalledWith(
                expect.objectContaining({
                    notification: expect.objectContaining({
                        title: "Chikara Academy MMO",
                    }),
                })
            );
        });

        it("should use custom image when provided", async () => {
            const payload: NotificationPayload = {
                body: "Test message",
                image: "https://example.com/custom-image.png",
            };

            mockMessaging.send.mockResolvedValue("message-id-123");

            await sendPushNotification(1, "test-token", payload);

            expect(mockMessaging.send).toHaveBeenCalledWith(
                expect.objectContaining({
                    notification: expect.objectContaining({
                        imageUrl: "https://example.com/custom-image.png",
                    }),
                    webpush: expect.objectContaining({
                        headers: {
                            image: "https://example.com/custom-image.png",
                        },
                    }),
                })
            );
        });

        it("should return null for invalid token", async () => {
            const payload: NotificationPayload = { body: "Test message" };

            const result1 = await sendPushNotification(1, "", payload);
            const result2 = await sendPushNotification(1, "x", payload);

            expect(result1).toBeNull();
            expect(result2).toBeNull();
            expect(mockMessaging.send).not.toHaveBeenCalled();
        });

        it("should return null when Firebase is not initialized", async () => {
            process.env.FIREBASE_ENABLED = "false";
            // Reset Firebase state
            (global as any).__firebaseApp = null;

            const payload: NotificationPayload = { body: "Test message" };
            const result = await sendPushNotification(1, "test-token", payload);

            expect(result).toBeNull();
            expect(mockMessaging.send).not.toHaveBeenCalled();
        });

        it("should handle Firebase messaging errors gracefully", async () => {
            const payload: NotificationPayload = { body: "Test message" };
            mockMessaging.send.mockRejectedValue(new Error("Firebase error"));

            const result = await sendPushNotification(1, "test-token", payload);

            expect(result).toBeNull();
        });

        it("should handle Firebase messaging errors with specific error codes", async () => {
            const payload: NotificationPayload = { body: "Test message" };
            const firebaseError = FirebaseMockHelper.createMockFirebaseError(
                "messaging/invalid-registration-token",
                "Invalid registration token"
            );
            mockMessaging.send.mockRejectedValue(firebaseError);

            const result = await sendPushNotification(1, "test-token", payload);

            expect(result).toBeNull();
        });
    });

    describe("sendPushNotifications", () => {
        beforeEach(async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);
            await initializeFirebase();
        });

        it("should send notifications to multiple tokens", async () => {
            const tokens = [
                { userId: 1, token: "token-1" },
                { userId: 2, token: "token-2" },
                { userId: 3, token: "token-3" },
            ];
            const payload: NotificationPayload = { body: "Test message" };

            mockMessaging.send.mockResolvedValue("message-id");

            await sendPushNotifications(tokens, payload);

            expect(mockMessaging.send).toHaveBeenCalledTimes(3);
        });

        it("should filter out invalid tokens", async () => {
            const tokens = [
                { userId: 1, token: "valid-token" },
                { userId: null, token: "invalid-user-token" },
                { userId: 2, token: "" },
                { userId: 3, token: "another-valid-token" },
            ];
            const payload: NotificationPayload = { body: "Test message" };

            mockMessaging.send.mockResolvedValue("message-id");

            await sendPushNotifications(tokens, payload);

            expect(mockMessaging.send).toHaveBeenCalledTimes(2);
        });

        it("should handle empty token array", async () => {
            const payload: NotificationPayload = { body: "Test message" };

            await sendPushNotifications([], payload);

            expect(mockMessaging.send).not.toHaveBeenCalled();
        });

        it("should handle mixed success and failure results", async () => {
            const tokens = [
                { userId: 1, token: "valid-token" },
                { userId: 2, token: "invalid-token" },
            ];
            const payload: NotificationPayload = { body: "Test message" };

            mockMessaging.send.mockResolvedValueOnce("message-id-1").mockRejectedValueOnce(new Error("Invalid token"));

            await sendPushNotifications(tokens, payload);

            expect(mockMessaging.send).toHaveBeenCalledTimes(2);
        });

        it("should return early when Firebase is not initialized", async () => {
            process.env.FIREBASE_ENABLED = "false";
            // Reset Firebase state
            (global as any).__firebaseApp = null;

            const tokens = [{ userId: 1, token: "test-token" }];
            const payload: NotificationPayload = { body: "Test message" };

            await sendPushNotifications(tokens, payload);

            expect(mockMessaging.send).not.toHaveBeenCalled();
        });
    });

    describe("Environment Configuration", () => {
        it("should handle missing APP_BASE_URL", async () => {
            delete process.env.APP_BASE_URL;

            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);
            await initializeFirebase();

            const payload: NotificationPayload = { body: "Test message" };
            mockMessaging.send.mockResolvedValue("message-id");

            await sendPushNotification(1, "test-token", payload);

            expect(mockMessaging.send).toHaveBeenCalledWith(
                expect.objectContaining({
                    webpush: expect.objectContaining({
                        fcmOptions: {
                            link: "https://api.battleacademy.io",
                        },
                    }),
                })
            );
        });

        it("should validate required environment variables", async () => {
            const requiredVars = ["FIREBASE_PRIVATE_KEY"];

            for (const varName of requiredVars) {
                const originalValue = process.env[varName];
                delete process.env[varName];

                await expect(initializeFirebase()).rejects.toThrow();

                process.env[varName] = originalValue;
            }
        });
    });

    describe("Legacy Support", () => {
        it("should support legacy sendPushNotificationLegacy function", async () => {
            const mockApp = FirebaseMockHelper.createMockFirebaseApp();
            mockFirebaseAdmin.initializeApp.mockReturnValue(mockApp);
            await initializeFirebase();

            const { sendPushNotificationLegacy } = await import("../firebase");
            mockMessaging.send.mockResolvedValue("message-id");

            const result = await sendPushNotificationLegacy(1, "test-token", "Legacy message");

            expect(result).toBe("message-id");
            expect(mockMessaging.send).toHaveBeenCalledWith(
                expect.objectContaining({
                    notification: expect.objectContaining({
                        body: "Legacy message",
                    }),
                })
            );
        });
    });
});
