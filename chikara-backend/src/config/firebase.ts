import { logger } from "../utils/log.js";
import * as admin from "firebase-admin";
import { App, cert, initializeApp } from "firebase-admin/app";
import { getMessaging, Message } from "firebase-admin/messaging";

// Configuration
const appBaseUrl = process.env.APP_BASE_URL || "https://api.battleacademy.io";
const isFirebaseEnabled = process.env.FIREBASE_ENABLED === "true";

// Firebase credentials from environment
const firebaseCredentials = {
    projectId: process.env.FIREBASE_PROJECT_ID || "chikara-academy",
    clientEmail: process.env.FIREBASE_CLIENT_EMAIL || "<EMAIL>",
    privateKey: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, "\n"),
};

// App instance
let firebaseApp: App | null = null;

/**
 * Initialize Firebase Admin SDK
 */
export const initializeFirebase = (): Promise<void> => {
    return new Promise<void>((resolve, reject) => {
        if (!isFirebaseEnabled) {
            logger.info("Firebase is disabled via FIREBASE_ENABLED environment variable");
            resolve();
            return;
        }

        try {
            if (!firebaseApp) {
                if (!firebaseCredentials.privateKey) {
                    throw new Error("FIREBASE_PRIVATE_KEY environment variable is required");
                }

                firebaseApp = initializeApp({
                    credential: cert(firebaseCredentials),
                });

                logger.info("Firebase Admin SDK initialized successfully");
            }
            resolve();
        } catch (error) {
            logger.error(`Failed to initialize Firebase Admin SDK: ${String(error)}`);
            reject(error);
        }
    });
};

/**
 * Get Firebase messaging instance
 */
export const getFirebaseMessaging = () => {
    if (!firebaseApp) {
        throw new Error("Firebase not initialized. Call initializeFirebase() first.");
    }
    return getMessaging(firebaseApp);
};

/**
 * Check if Firebase is enabled and initialized
 */
export const isFirebaseInitialized = (): boolean => {
    return isFirebaseEnabled && firebaseApp !== null;
};

/**
 * Notification payload interface
 */
export interface NotificationPayload {
    title?: string;
    body: string;
    image?: string;
    data?: Record<string, string>;
}

/**
 * Send push notification to a specific token
 */
export async function sendPushNotification(
    userId: number,
    token: string,
    payload: NotificationPayload
): Promise<string | null> {
    if (!isFirebaseInitialized()) {
        logger.debug("Firebase not initialized, skipping push notification");
        return null;
    }

    if (!token || token.length <= 1) {
        logger.debug(`Invalid token for userId: ${userId}`);
        return null;
    }

    const message: Message = {
        token,
        notification: {
            title: payload.title || "Chikara Academy MMO",
            body: payload.body,
            imageUrl: payload.image || "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
        },
        webpush: {
            headers: {
                image: payload.image || "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
            },
            fcmOptions: {
                link: appBaseUrl,
            },
        },
        android: {
            notification: {
                imageUrl: payload.image || "https://d13cmcqz8qkryo.cloudfront.net/static/misc/vX6Dmcr.png",
            },
        },
        data: payload.data,
    };

    try {
        const messaging = getFirebaseMessaging();
        const response = await messaging.send(message);

        if (response) {
            logger.info(`Push notification sent for userId: ${userId}, response ID: ${response}`);
            return response;
        }
    } catch (error) {
        logger.debug(`Failed to send push notification to userId: ${userId}, error: ${String(error)}`);
    }

    return null;
}

/**
 * Send push notifications to multiple tokens
 */
export async function sendPushNotifications(
    tokens: { userId: number | null; token: string }[],
    payload: NotificationPayload
): Promise<void> {
    if (!isFirebaseInitialized()) {
        logger.debug("Firebase not initialized, skipping push notifications");
        return;
    }

    if (tokens.length === 0) {
        logger.debug("No tokens provided for push notifications");
        return;
    }

    const promises = tokens
        .filter((tokenRecord) => tokenRecord.userId && tokenRecord.token)
        .map((tokenRecord) => sendPushNotification(tokenRecord.userId!, tokenRecord.token, payload));

    const results = await Promise.allSettled(promises);

    // Log summary
    const successful = results.filter((result) => result.status === "fulfilled" && result.value).length;
    const failed = results.length - successful;

    logger.info(`Push notifications sent: ${successful} successful, ${failed} failed out of ${tokens.length} tokens`);
}

/**
 * Legacy function for backward compatibility
 */
export async function sendPushNotificationLegacy(
    userId: number,
    token: string,
    message: string
): Promise<string | null> {
    return sendPushNotification(userId, token, { body: message });
}

// Export Firebase admin for direct access if needed
export { admin };
