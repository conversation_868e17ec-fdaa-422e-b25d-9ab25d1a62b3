/**
 * Tests for notification service
 */
import { describe, it, expect, beforeEach, vi } from "vitest";
import {
    NotifyUser,
    sendSinglePushNotification,
    sendNotificationPush,
    createNotification,
    fetchUserPushTokens,
} from "../notification.service";
import { NotificationTypes } from "../types/notification";
import { TestDataFactory, FirebaseMockHelper, mockPrismaClient } from "../test/setup";

// Mock Firebase
vi.mock("../config/firebase.js", () => ({
    sendPushNotifications: vi.fn(),
    NotificationPayload: {},
}));

// Mock socket service
const mockSockets = {
    SendNotification: vi.fn(),
    NotifyMessageRemoved: vi.fn(),
};

vi.mock("../config/socket.js", () => mockSockets);

// Mock database
vi.mock("../lib/db.js", () => ({
    db: mockPrismaClient,
}));

// Mock logger
const mockLogger = {
    debug: vi.fn(),
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
};

vi.mock("../utils/log.js", () => ({
    logger: mockLogger,
    LogErrorStack: vi.fn(),
}));

describe("Notification Service", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("createNotification", () => {
        it("should create notification successfully", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            const result = await createNotification(1, NotificationTypes.levelup, JSON.stringify({ level: 5 }), false);

            expect(result).toEqual(mockNotification);
            expect(mockPrismaClient.notification.create).toHaveBeenCalledWith({
                data: {
                    notificationType: NotificationTypes.levelup,
                    details: JSON.stringify({ level: 5 }),
                    read: false,
                    userId: 1,
                },
            });
        });

        it("should handle database errors", async () => {
            mockPrismaClient.notification.create.mockRejectedValue(new Error("Database error"));

            await expect(createNotification(1, NotificationTypes.levelup, "{}", false)).rejects.toThrow(
                "Database error"
            );
        });
    });

    describe("fetchUserPushTokens", () => {
        it("should fetch user push tokens successfully", async () => {
            const mockTokens = [
                { userId: 1, token: "token-1" },
                { userId: 1, token: "token-2" },
            ];
            mockPrismaClient.push_token.findMany.mockResolvedValue(mockTokens);

            const result = await fetchUserPushTokens(1);

            expect(result).toEqual(mockTokens);
            expect(mockPrismaClient.push_token.findMany).toHaveBeenCalledWith({
                where: { userId: 1 },
                select: {
                    userId: true,
                    token: true,
                },
            });
        });

        it("should return empty array when no tokens found", async () => {
            mockPrismaClient.push_token.findMany.mockResolvedValue([]);

            const result = await fetchUserPushTokens(1);

            expect(result).toEqual([]);
        });

        it("should handle database errors", async () => {
            mockPrismaClient.push_token.findMany.mockRejectedValue(new Error("Database error"));

            await expect(fetchUserPushTokens(1)).rejects.toThrow("Database error");
        });
    });

    describe("NotifyUser", () => {
        it("should create notification and send socket notification", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            await NotifyUser(
                1,
                NotificationTypes.levelup,
                { level: 5, experience: 1000 },
                false,
                false // Don't send push notification
            );

            expect(mockPrismaClient.notification.create).toHaveBeenCalledWith({
                data: {
                    notificationType: NotificationTypes.levelup,
                    details: JSON.stringify({ level: 5, experience: 1000 }),
                    read: false,
                    userId: 1,
                },
            });

            expect(mockSockets.SendNotification).toHaveBeenCalledWith(1, {
                type: NotificationTypes.levelup,
                details: JSON.stringify({ level: 5, experience: 1000 }),
            });
        });

        it("should not create notification for message type", async () => {
            await NotifyUser(1, NotificationTypes.message, { content: "Hello" }, false, false);

            expect(mockPrismaClient.notification.create).not.toHaveBeenCalled();
            expect(mockSockets.SendNotification).toHaveBeenCalledWith(1, {
                type: NotificationTypes.message,
                details: JSON.stringify({ content: "Hello" }),
            });
        });

        it("should use default read status for certain notification types", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            // Test with hospitalised type (should be read by default)
            await NotifyUser(
                1,
                NotificationTypes.hospitalised,
                { reason: "battle" },
                undefined, // Let it use default
                false
            );

            expect(mockPrismaClient.notification.create).toHaveBeenCalledWith({
                data: {
                    notificationType: NotificationTypes.hospitalised,
                    details: JSON.stringify({ reason: "battle" }),
                    read: true, // Should be true by default
                    userId: 1,
                },
            });
        });

        it("should send push notification when enabled", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            // Mock user with push notifications enabled
            mockPrismaClient.user.findUnique.mockResolvedValue({
                id: 1,
                pushNotificationsEnabled: true,
            });

            // Mock push tokens
            mockPrismaClient.push_token.findMany.mockResolvedValue([{ userId: 1, token: "test-token" }]);

            const { sendPushNotifications } = await import("../config/firebase.js");
            (sendPushNotifications as any).mockResolvedValue(undefined);

            await NotifyUser(
                1,
                NotificationTypes.levelup,
                { level: 5 },
                false,
                true // Send push notification
            );

            expect(sendPushNotifications).toHaveBeenCalled();
        });

        it("should not send push notification for excluded types", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            const { sendPushNotifications } = await import("../config/firebase.js");

            // Test with temporary_notification (should be excluded)
            await NotifyUser(1, NotificationTypes.temporary_notification, { message: "temp" }, false, true);

            expect(sendPushNotifications).not.toHaveBeenCalled();
        });

        it("should handle errors gracefully", async () => {
            mockPrismaClient.notification.create.mockRejectedValue(new Error("Database error"));

            // Should not throw
            await NotifyUser(1, NotificationTypes.levelup, { level: 5 });

            expect(mockLogger.debug).toHaveBeenCalled();
        });
    });

    describe("sendSinglePushNotification", () => {
        it("should send push notification with string payload", async () => {
            const mockTokens = [{ userId: 1, token: "test-token" }];
            mockPrismaClient.push_token.findMany.mockResolvedValue(mockTokens);

            const { sendPushNotifications } = await import("../config/firebase.js");
            (sendPushNotifications as any).mockResolvedValue(undefined);

            const result = await sendSinglePushNotification(1, "Test message");

            expect(result).toBe(true);
            expect(sendPushNotifications).toHaveBeenCalledWith(mockTokens, {
                body: "Test message",
            });
        });

        it("should send push notification with payload object", async () => {
            const mockTokens = [{ userId: 1, token: "test-token" }];
            mockPrismaClient.push_token.findMany.mockResolvedValue(mockTokens);

            const { sendPushNotifications } = await import("../config/firebase.js");
            (sendPushNotifications as any).mockResolvedValue(undefined);

            const payload = {
                title: "Custom Title",
                body: "Custom message",
                data: { key: "value" },
            };

            const result = await sendSinglePushNotification(1, payload);

            expect(result).toBe(true);
            expect(sendPushNotifications).toHaveBeenCalledWith(mockTokens, payload);
        });

        it("should return false when no tokens found", async () => {
            mockPrismaClient.push_token.findMany.mockResolvedValue([]);

            const result = await sendSinglePushNotification(1, "Test message");

            expect(result).toBe(false);
        });

        it("should handle errors and return false", async () => {
            mockPrismaClient.push_token.findMany.mockRejectedValue(new Error("Database error"));

            const result = await sendSinglePushNotification(1, "Test message");

            expect(result).toBe(false);
        });
    });

    describe("sendNotificationPush", () => {
        it("should send push notification with user preference check", async () => {
            // Mock user with push notifications enabled
            mockPrismaClient.user.findUnique.mockResolvedValue({
                id: 1,
                pushNotificationsEnabled: true,
            });

            const mockTokens = [{ userId: 1, token: "test-token" }];
            mockPrismaClient.push_token.findMany.mockResolvedValue(mockTokens);

            const { sendPushNotifications } = await import("../config/firebase.js");
            (sendPushNotifications as any).mockResolvedValue(undefined);

            const result = await sendNotificationPush(1, NotificationTypes.levelup, { level: 5, experience: 1000 });

            expect(result).toBe(true);
            expect(mockPrismaClient.user.findUnique).toHaveBeenCalledWith({
                where: { id: 1 },
                select: { pushNotificationsEnabled: true },
            });
            expect(sendPushNotifications).toHaveBeenCalledWith(
                mockTokens,
                expect.objectContaining({
                    title: "Level Up!",
                    body: "Congratulations! You reached level 5!",
                    data: {
                        type: NotificationTypes.levelup,
                        details: JSON.stringify({ level: 5, experience: 1000 }),
                        userId: "1",
                    },
                })
            );
        });

        it("should not send when user has push notifications disabled", async () => {
            mockPrismaClient.user.findUnique.mockResolvedValue({
                id: 1,
                pushNotificationsEnabled: false,
            });

            const result = await sendNotificationPush(1, NotificationTypes.levelup, { level: 5 });

            expect(result).toBe(false);
            expect(mockPrismaClient.push_token.findMany).not.toHaveBeenCalled();
        });

        it("should return false when no tokens found", async () => {
            mockPrismaClient.user.findUnique.mockResolvedValue({
                id: 1,
                pushNotificationsEnabled: true,
            });
            mockPrismaClient.push_token.findMany.mockResolvedValue([]);

            const result = await sendNotificationPush(1, NotificationTypes.levelup, { level: 5 });

            expect(result).toBe(false);
        });

        it("should generate correct notification titles and bodies", async () => {
            mockPrismaClient.user.findUnique.mockResolvedValue({
                id: 1,
                pushNotificationsEnabled: true,
            });
            mockPrismaClient.push_token.findMany.mockResolvedValue([{ userId: 1, token: "test-token" }]);

            const { sendPushNotifications } = await import("../config/firebase.js");
            (sendPushNotifications as any).mockResolvedValue(undefined);

            // Test different notification types
            const testCases = [
                {
                    type: NotificationTypes.fight_win,
                    details: { opponent: "TestUser" },
                    expectedTitle: "Battle Victory!",
                    expectedBody: "You won a battle against TestUser!",
                },
                {
                    type: NotificationTypes.transfer_received,
                    details: { amount: 1000, sender: "Friend" },
                    expectedTitle: "Money Received",
                    expectedBody: "You received ¥1000 from Friend!",
                },
                {
                    type: NotificationTypes.quest_complete,
                    details: { questName: "First Quest" },
                    expectedTitle: "Quest Complete",
                    expectedBody: 'Quest "First Quest" completed!',
                },
            ];

            for (const testCase of testCases) {
                vi.clearAllMocks();

                await sendNotificationPush(1, testCase.type, testCase.details);

                expect(sendPushNotifications).toHaveBeenCalledWith(
                    expect.any(Array),
                    expect.objectContaining({
                        title: testCase.expectedTitle,
                        body: testCase.expectedBody,
                    })
                );
            }
        });

        it("should use default title and body for unknown notification types", async () => {
            mockPrismaClient.user.findUnique.mockResolvedValue({
                id: 1,
                pushNotificationsEnabled: true,
            });
            mockPrismaClient.push_token.findMany.mockResolvedValue([{ userId: 1, token: "test-token" }]);

            const { sendPushNotifications } = await import("../config/firebase.js");
            (sendPushNotifications as any).mockResolvedValue(undefined);

            await sendNotificationPush(1, "unknown_type" as NotificationTypes, { message: "Custom message" });

            expect(sendPushNotifications).toHaveBeenCalledWith(
                expect.any(Array),
                expect.objectContaining({
                    title: "Chikara Academy",
                    body: "Custom message",
                })
            );
        });

        it("should handle errors and return false", async () => {
            mockPrismaClient.user.findUnique.mockRejectedValue(new Error("Database error"));

            const result = await sendNotificationPush(1, NotificationTypes.levelup, { level: 5 });

            expect(result).toBe(false);
        });
    });

    describe("Notification Message Generation", () => {
        it("should generate appropriate messages for different notification types", () => {
            // This would test the private functions getNotificationTitle and getNotificationBody
            // Since they're private, we test them through sendNotificationPush

            const testCases = [
                {
                    type: NotificationTypes.crafting_completion,
                    details: { itemName: "Sword" },
                    expectedBody: "Your Sword has finished crafting!",
                },
                {
                    type: NotificationTypes.gang_invite,
                    details: { gangName: "Warriors" },
                    expectedBody: "Warriors has invited you to join!",
                },
                {
                    type: NotificationTypes.friend_request,
                    details: { username: "NewFriend" },
                    expectedBody: "NewFriend sent you a friend request!",
                },
                {
                    type: NotificationTypes.bounty_placed,
                    details: { amount: 5000 },
                    expectedBody: "A bounty of ¥5000 has been placed on you!",
                },
                {
                    type: NotificationTypes.lottery_won,
                    details: { amount: 10000 },
                    expectedBody: "You won ¥10000 in the lottery!",
                },
            ];

            // These would be tested through integration with sendNotificationPush
            testCases.forEach((testCase) => {
                expect(testCase.expectedBody).toBeTruthy();
            });
        });
    });

    describe("Error Handling and Edge Cases", () => {
        it("should handle null/undefined details gracefully", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            await NotifyUser(1, NotificationTypes.levelup, null as any);

            expect(mockPrismaClient.notification.create).toHaveBeenCalledWith({
                data: {
                    notificationType: NotificationTypes.levelup,
                    details: "null",
                    read: false,
                    userId: 1,
                },
            });
        });

        it("should handle circular reference in details", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            const circularObj: any = { name: "test" };
            circularObj.self = circularObj;

            // Should not throw due to circular reference
            await expect(NotifyUser(1, NotificationTypes.levelup, circularObj)).rejects.toThrow(); // JSON.stringify will throw on circular reference
        });

        it("should handle very large notification details", async () => {
            const mockNotification = TestDataFactory.createNotification(1);
            mockPrismaClient.notification.create.mockResolvedValue(mockNotification);

            const largeDetails = {
                data: "x".repeat(10000), // Large string
                array: new Array(1000).fill("item"),
            };

            await NotifyUser(1, NotificationTypes.levelup, largeDetails);

            expect(mockPrismaClient.notification.create).toHaveBeenCalled();
        });
    });
});
