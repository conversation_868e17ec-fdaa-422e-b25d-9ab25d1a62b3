import { sendPushNotifications, NotificationPayload } from "../config/firebase.js";
import * as sockets from "../config/socket.js";
import { db } from "../lib/db.js";
import { NotificationTypes } from "../types/notification.js";
import { LogErrorStack, logger } from "../utils/log.js";

const READ_BY_DEFAULT_NOTIFICATION_TYPES = new Set<NotificationTypes>([
    NotificationTypes.hospitalised,
    NotificationTypes.jail,
    NotificationTypes.transfer_sent,
    NotificationTypes.fight_win,
    NotificationTypes.banned,
]);

export const createNotification = async (
    userId: number,
    notificationType: NotificationTypes,
    details: string,
    read: boolean
) => {
    return await db.notification.create({
        data: {
            notificationType,
            details,
            read,
            userId,
        },
    });
};

export const fetchUserPushTokens = async (userId: number) => {
    return await db.push_token.findMany({
        where: { userId },
        select: {
            userId: true,
            token: true,
        },
    });
};

export async function NotifyUser(
    userId: number,
    notificationType: NotificationTypes,
    details: Record<string, unknown>,
    read?: boolean,
    sendPush: boolean = true
): Promise<void> {
    try {
        const normalisedDetails = JSON.stringify(details);
        logger.debug("Notification details: " + normalisedDetails);

        // Create database notification (except for messages)
        if (notificationType !== NotificationTypes.message) {
            await createNotification(
                userId,
                notificationType,
                normalisedDetails,
                read ?? READ_BY_DEFAULT_NOTIFICATION_TYPES.has(notificationType)
            );
        }

        // Send real-time socket notification
        sockets.SendNotification(userId, {
            type: notificationType,
            details: normalisedDetails,
        });

        // Send push notification if enabled and user preferences allow
        if (sendPush && shouldSendPushNotification(notificationType)) {
            await sendNotificationPush(userId, notificationType, details);
        }
    } catch (error) {
        LogErrorStack({ message: "Failed to send notification:", error });
    }
}

/**
 * Determine if push notification should be sent for this notification type
 */
function shouldSendPushNotification(type: NotificationTypes): boolean {
    // Don't send push for these notification types
    const excludedTypes = new Set<NotificationTypes>([
        NotificationTypes.message, // Messages are handled separately
        NotificationTypes.temporary_notification, // Temporary notifications shouldn't push
    ]);

    return !excludedTypes.has(type);
}

export function NotifyMessageRemoved(): void {
    // TODO: support other chat rooms
    sockets.NotifyMessageRemoved();
}

/**
 * Send push notification to a single user
 */
export async function sendSinglePushNotification(
    userId: number,
    payload: NotificationPayload | string
): Promise<boolean> {
    try {
        // Fetch the user's push tokens from the database
        const pushTokens = await fetchUserPushTokens(userId);

        if (pushTokens.length === 0) {
            logger.debug(`No push tokens found for user: ${userId}`);
            return false;
        }

        // Convert string message to payload format for backward compatibility
        const notificationPayload: NotificationPayload = typeof payload === "string" ? { body: payload } : payload;

        await sendPushNotifications(pushTokens, notificationPayload);
        return true;
    } catch (error) {
        LogErrorStack({ message: "Error sending push notification:", error });
        return false;
    }
}

/**
 * Send push notification with notification type context
 */
export async function sendNotificationPush(
    userId: number,
    notificationType: NotificationTypes,
    details: Record<string, unknown>
): Promise<boolean> {
    try {
        // Check if user has push notifications enabled
        const user = await db.user.findUnique({
            where: { id: userId },
            select: { pushNotificationsEnabled: true },
        });

        if (!user?.pushNotificationsEnabled) {
            logger.debug(`Push notifications disabled for user: ${userId}`);
            return false;
        }

        const pushTokens = await fetchUserPushTokens(userId);

        if (pushTokens.length === 0) {
            logger.debug(`No push tokens found for user: ${userId}`);
            return false;
        }

        // Create notification payload based on type
        const payload: NotificationPayload = {
            title: getNotificationTitle(notificationType),
            body: getNotificationBody(notificationType, details),
            data: {
                type: notificationType,
                details: JSON.stringify(details),
                userId: userId.toString(),
            },
        };

        await sendPushNotifications(pushTokens, payload);
        return true;
    } catch (error) {
        LogErrorStack({ message: "Error sending notification push:", error });
        return false;
    }
}

/**
 * Get notification title based on type
 */
function getNotificationTitle(type: NotificationTypes): string {
    const titles: Partial<Record<NotificationTypes, string>> = {
        [NotificationTypes.levelup]: "Level Up!",
        [NotificationTypes.fight_win]: "Battle Victory!",
        [NotificationTypes.fight_win_attacked]: "Battle Won!",
        [NotificationTypes.transfer_received]: "Money Received",
        [NotificationTypes.quest_complete]: "Quest Complete",
        [NotificationTypes.crafting_completion]: "Crafting Complete",
        [NotificationTypes.gang_invite]: "Gang Invitation",
        [NotificationTypes.friend_request]: "Friend Request",
        [NotificationTypes.bounty_placed]: "Bounty Alert",
        [NotificationTypes.lottery_won]: "Lottery Winner!",
    };

    return titles[type] || "Chikara Academy";
}

/**
 * Get notification body based on type and details
 */
function getNotificationBody(type: NotificationTypes, details: Record<string, unknown>): string {
    // You can customize messages based on notification type and details
    switch (type) {
        case NotificationTypes.levelup:
            return `Congratulations! You reached level ${details.level || "up"}!`;
        case NotificationTypes.fight_win:
        case NotificationTypes.fight_win_attacked:
            return `You won a battle against ${details.opponent || "an opponent"}!`;
        case NotificationTypes.transfer_received:
            return `You received ¥${details.amount || "0"} from ${details.sender || "someone"}!`;
        case NotificationTypes.quest_complete:
            return `Quest "${details.questName || "Unknown"}" completed!`;
        case NotificationTypes.crafting_completion:
            return `Your ${details.itemName || "item"} has finished crafting!`;
        case NotificationTypes.gang_invite:
            return `${details.gangName || "A gang"} has invited you to join!`;
        case NotificationTypes.friend_request:
            return `${details.username || "Someone"} sent you a friend request!`;
        case NotificationTypes.bounty_placed:
            return `A bounty of ¥${details.amount || "0"} has been placed on you!`;
        case NotificationTypes.lottery_won:
            return `You won ¥${details.amount || "0"} in the lottery!`;
        default:
            return (details.message as string) || "You have a new notification!";
    }
}

export { sendPushNotifications } from "../config/firebase.js";

export default {
    NotifyUser,
    NotifyMessageRemoved,
    fetchUserPushTokens,
    sendPushNotifications,
    sendSinglePushNotification,
};
