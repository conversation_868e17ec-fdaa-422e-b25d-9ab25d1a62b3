import { craftingConfig } from "../../config/gameConfig.js";
import * as InventoryService from "../../core/inventory.service.js";
import * as UserService from "../../core/user.service.js";
import * as CraftingHelpers from "./crafting.helpers.js";
import * as CraftingRepository from "../../repositories/crafting.repository.js";
import * as UserRepository from "../../repositories/user.repository.js";
import type { CraftingRecipe, RecipeItemInfo } from "./crafting.types.js";
import * as ShrineHelper from "../shrine/shrine.helpers.js";
import * as TalentHelper from "../talents/talents.helpers.js";
import * as SkillsService from "../../core/skills.service.js";
import { logAction } from "../../lib/actionLogger.js";
import type { UserModel } from "../../lib/db.js";
import { LogErrorStack } from "../../utils/log.js";
import { hasExpired } from "../../utils/dateHelpers.js";
import { SkillType } from "@prisma/client";
import { emitItemCrafted } from "../../core/events/index.js";

const { CRAFTING_ENERGY_COST } = craftingConfig.public;

export const getCraftingQueue = async (userId: number) => {
    const activeCrafts = await CraftingRepository.findCraftingQueueByUserId(userId);

    const formattedCrafts = activeCrafts
        .filter((craft) => craft.crafting_recipe)
        .map((craft) => {
            const recipe = craft.crafting_recipe;
            if (!recipe) return null;

            const inputs: RecipeItemInfo[] = [];
            const outputs: RecipeItemInfo[] = [];

            for (const recipeItem of recipe.recipe_item) {
                const item = recipeItem.item;

                const recipeItemInfo: RecipeItemInfo = {
                    ...item,
                    amount: recipeItem.count || 0,
                };

                if (recipeItem.itemType === "input") {
                    inputs.push(recipeItemInfo);
                } else {
                    outputs.push(recipeItemInfo);
                }
            }

            return {
                ...craft,
                crafting_recipe: {
                    id: recipe.id,
                    craftTime: recipe.craftTime,
                    requiredSkillType: recipe.requiredSkillType,
                    requiredSkillLevel: recipe.requiredSkillLevel,
                    inputs,
                    outputs,
                },
            };
        })
        .filter(Boolean);

    return { data: formattedCrafts };
};

export const recipeList = async (userId: number, isAdminRoute = false) => {
    const userRecipes = await CraftingRepository.findUserRecipesByUserId(userId);
    const userRecipeIds = userRecipes.map((ur) => ur.craftingRecipeId) || [];

    const recipes = await CraftingRepository.findRecipesByLevelAndIds(userRecipeIds);

    const formattedRecipes: CraftingRecipe[] = [];

    for (const recipe of recipes) {
        // Check if user meets skill requirement for the recipe
        if (!isAdminRoute && recipe.requiredSkillType && recipe.requiredSkillLevel > 0) {
            const userSkillLevel = await SkillsService.getSkillLevel(userId, recipe.requiredSkillType as SkillType);

            // Skip this recipe if user doesn't meet the skill requirement
            if (userSkillLevel < recipe.requiredSkillLevel) {
                continue;
            }
        }

        const inputs: RecipeItemInfo[] = [];
        const outputs: RecipeItemInfo[] = [];

        for (const recipeItem of recipe.recipe_item) {
            const item = recipeItem.item;

            const recipeItemInfo: RecipeItemInfo = {
                ...item,
                amount: recipeItem.count || 0,
            };

            if (recipeItem.itemType === "input") {
                inputs.push(recipeItemInfo);
            } else {
                outputs.push(recipeItemInfo);
            }
        }

        formattedRecipes.push({
            id: recipe.id,
            craftTime: recipe.craftTime,
            requiredSkillType: recipe.requiredSkillType,
            requiredSkillLevel: recipe.requiredSkillLevel,
            inputs,
            outputs,
        });
    }

    return { data: formattedRecipes };
};

export const craftItem = async (user: UserModel, craftData: { recipeId: number; amount?: number }) => {
    const recipe = await CraftingRepository.findRecipeById(craftData.recipeId);
    const amount = Number.parseInt(String(craftData.amount)) || 1;

    if (!recipe) {
        return { error: "Invalid recipe", statusCode: 400 };
    }

    const skillType = recipe.requiredSkillType;

    // Check if user meets the required skill level for crafting
    if (skillType && recipe.requiredSkillLevel > 0) {
        const userSkillLevel = await SkillsService.getSkillLevel(user.id, skillType as SkillType);
        if (userSkillLevel < recipe.requiredSkillLevel) {
            return {
                error: `You need ${skillType} level ${recipe.requiredSkillLevel} to craft this item.`,
                statusCode: 400,
            };
        }
    }

    const inputItems = await CraftingHelpers.GetInputs(craftData.recipeId);

    // Check that user has all the required items
    for (const inputItem of inputItems) {
        if (!(await InventoryService.UserHasNumberOfItem(user.id, inputItem.itemId, (inputItem.count ?? 0) * amount))) {
            return { error: "You don't have enough items!", statusCode: 400 };
        }
    }

    const currentUser = await UserRepository.getUserById(user.id);

    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }

    const activeCrafts = await CraftingRepository.findCraftingQueueByUserId(currentUser.id);

    const multiTaskerTalent = await TalentHelper.UserHasMultiTaskerTalent(currentUser.id);
    const maxSlots = 1 + (multiTaskerTalent?.level || 0);

    if (maxSlots - activeCrafts.length < amount) {
        return { error: "Not enough free crafting slots", statusCode: 400 };
    }

    if (recipe.cost || CRAFTING_ENERGY_COST > 0) {
        if (currentUser.cash < (recipe.cost ?? 0) * amount) {
            return { error: "Not enough cash to craft recipe", statusCode: 400 };
        }

        if (user.energy < CRAFTING_ENERGY_COST * amount) {
            return { error: "Not enough energy", statusCode: 400 };
        }

        await UserService.updateUser(currentUser.id, {
            cash: { decrement: (recipe.cost ?? 0) * amount },
            energy: { decrement: CRAFTING_ENERGY_COST * amount },
            lastEnergyTick: Date.now(), // Start energy regeneration timer
        });
    }

    // Take away all the input items from the user
    try {
        for (const inputItem of inputItems) {
            await InventoryService.SubtractItemFromUser({
                userId: user.id,
                itemId: inputItem.itemId,
                amount: (inputItem.count ?? 0) * amount,
            });
        }
    } catch (error) {
        LogErrorStack({ message: `Error when taking away items from user ${user.id}: `, error });
        return { error: "Invalid items", statusCode: 400 };
    }

    const shrineBuffActive = await ShrineHelper.dailyBuffIsActive("craftSpeed");
    const speedCrafter = await TalentHelper.UserHasSpeedCrafterTalent(user.id);
    let craftTime = speedCrafter ? recipe.craftTime * (speedCrafter.modifier ?? 1) : recipe.craftTime;
    if (shrineBuffActive) {
        craftTime *= shrineBuffActive;
    }

    // Create new crafting queue entries
    const now = Date.now();
    const endsAt = now + craftTime;

    for (let i = 0; i < amount; i++) {
        await CraftingRepository.createUserCraftingQueue({
            user: {
                connect: {
                    id: user.id,
                },
            },
            crafting_recipe: {
                connect: {
                    id: recipe.id,
                },
            },
            startedAt: now,
            endsAt: endsAt,
        });

        logAction({
            action: "CRAFT_STARTED",
            userId: currentUser.id,
            info: {
                recipeId: recipe.id,
            },
        });
    }

    return { data: "Crafting started successfully" };
};

export const completeCraft = async (userId: number, craftId: number) => {
    const activeCraft = await CraftingRepository.findCraftingQueueByUserIdAndCraftId(userId, craftId);

    if (!activeCraft) {
        return { error: "Invalid craft", statusCode: 400 };
    }

    const currentUser = await UserRepository.getUserById(userId);
    if (!currentUser) {
        return { error: "User not found", statusCode: 404 };
    }

    if (!hasExpired(activeCraft.endsAt)) {
        return { error: "Not finished", statusCode: 400 };
    }

    const recipeIdToComplete = activeCraft.craftingRecipeId;
    if (!recipeIdToComplete) {
        await CraftingRepository.deleteCraftingQueue(activeCraft);
        return { error: "Recipe not found for this craft", statusCode: 404 };
    }

    await CraftingRepository.deleteCraftingQueue(activeCraft);

    await CraftingHelpers.ApplyCraftCompletion(currentUser.id, recipeIdToComplete);

    // Get the recipe to determine which skill experience to award
    const recipe = await CraftingRepository.findRecipeById(recipeIdToComplete);

    let skillExpResult = null;
    let baseExpGain = 0;

    // Award skill experience if the recipe has a required skill type
    if (recipe?.requiredSkillType) {
        // The base experience gained is related to the recipe's required skill level
        // Higher level recipes give more experience
        baseExpGain = Math.max(10, (recipe.requiredSkillLevel || 1) * 5);

        skillExpResult = await SkillsService.addSkillExp(userId, recipe.requiredSkillType as SkillType, baseExpGain);

        if (skillExpResult.leveledUp) {
            logAction({
                action: "SKILL_LEVEL_UP",
                userId: userId,
                info: {
                    skill: recipe.requiredSkillType,
                    previousLevel: skillExpResult.previousLevel,
                    newLevel: skillExpResult.currentLevel,
                    levelsGained: skillExpResult.levelsGained,
                },
            });
        }
    }

    const outputItems = await CraftingHelpers.GetOutputs(recipeIdToComplete);
    for (const outputItem of outputItems) {
        // Emit item crafted event
        await emitItemCrafted({
            userId: currentUser.id,
            itemId: outputItem.itemId,
            quantity: outputItem.count ?? 1,
        });
    }

    logAction({
        action: "CRAFT_COMPLETED",
        userId: userId,
        info: {
            recipeId: recipeIdToComplete,
        },
    });

    return {
        data: {
            status: "Success",
            skillExp: skillExpResult
                ? {
                      skill: recipe?.requiredSkillType,
                      expGained: baseExpGain,
                      leveledUp: skillExpResult.leveledUp,
                      currentLevel: skillExpResult.currentLevel,
                      currentExp: skillExpResult.currentExp,
                      expToNextLevel: skillExpResult.expToNextLevel,
                  }
                : null,
        },
    };
};

export const cancelCraft = async (userId: number, craftId: number) => {
    const activeCraft = await CraftingRepository.findCraftingQueueByUserIdAndCraftId(userId, craftId);

    if (!activeCraft) {
        return { error: "Invalid craft", statusCode: 400 };
    }

    const recipeIdToCancel = activeCraft.craftingRecipeId;
    await CraftingRepository.deleteCraftingQueue(activeCraft);

    if (recipeIdToCancel) {
        await CraftingHelpers.ReturnCraftingItems(userId, recipeIdToCancel);
    }

    logAction({
        action: "CRAFT_CANCELLED",
        userId: userId,
        info: {
            recipeId: recipeIdToCancel,
        },
    });

    return { data: "Success" };
};
