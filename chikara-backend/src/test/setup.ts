/**
 * Test setup and configuration for backend tests
 */
import { beforeAll, afterAll, beforeEach, afterEach } from "vitest";
import { PrismaClient } from "@prisma/client";
import { mockDeep, mockReset, DeepMockProxy } from "vitest-mock-extended";
import * as admin from "firebase-admin";

// Mock Firebase Admin
export const mockFirebaseAdmin = mockDeep<typeof admin>();

// Mock Prisma Client
export const mockPrismaClient = mockDeep<PrismaClient>();

// Test database instance
let testDb: PrismaClient;

// Setup test environment
beforeAll(async () => {
    // Set test environment variables
    process.env.NODE_ENV = "test";
    process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || "file:./test.db";
    process.env.FIREBASE_ENABLED = "false"; // Disable Firebase for most tests
    process.env.LOG_LEVEL = "error"; // Reduce log noise in tests

    // Initialize test database
    testDb = new PrismaClient({
        datasources: {
            db: {
                url: process.env.DATABASE_URL,
            },
        },
    });

    // Run database migrations for tests
    // Note: In a real setup, you'd run migrations here
    console.log("Test database initialized");
});

// Cleanup after all tests
afterAll(async () => {
    if (testDb) {
        await testDb.$disconnect();
    }
});

// Reset mocks before each test
beforeEach(() => {
    mockReset(mockFirebaseAdmin);
    mockReset(mockPrismaClient);
});

// Cleanup after each test
afterEach(async () => {
    // Clean up test data if using real database
    if (process.env.USE_REAL_DB_FOR_TESTS === "true" && testDb) {
        // Clean up test data
        await testDb.push_token.deleteMany({});
        await testDb.notification.deleteMany({});
        // Add other cleanup as needed
    }
});

// Test utilities
export const createTestUser = (overrides: Partial<any> = {}) => ({
    id: 1,
    username: "testuser",
    email: "<EMAIL>",
    pushNotificationsEnabled: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    ...overrides,
});

export const createTestPushToken = (overrides: Partial<any> = {}) => ({
    id: 1,
    userId: 1,
    token: "test-fcm-token-" + Math.random().toString(36).substring(7),
    createdAt: new Date(),
    ...overrides,
});

export const createTestNotification = (overrides: Partial<any> = {}) => ({
    id: 1,
    userId: 1,
    notificationType: "levelup",
    details: JSON.stringify({ level: 5 }),
    read: false,
    createdAt: new Date(),
    ...overrides,
});

// Mock Firebase messaging
export const mockFirebaseMessaging = {
    send: vi.fn().mockResolvedValue("mock-message-id"),
    sendMulticast: vi.fn().mockResolvedValue({
        successCount: 1,
        failureCount: 0,
        responses: [{ success: true, messageId: "mock-message-id" }],
    }),
};

// Mock Firebase app
export const mockFirebaseApp = {
    name: "test-app",
    options: {},
};

// Export test database for use in tests
export { testDb };
