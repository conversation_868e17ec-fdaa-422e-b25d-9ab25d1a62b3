/**
 * Test utilities and helpers for backend testing
 */
import { PrismaClient } from "@prisma/client";
import { NotificationTypes } from "../types/notification.js";

// Test data factories
export class TestDataFactory {
    static createUser(overrides: Partial<any> = {}) {
        return {
            id: Math.floor(Math.random() * 1000) + 1,
            username: `testuser_${Math.random().toString(36).substring(7)}`,
            email: `test_${Math.random().toString(36).substring(7)}@example.com`,
            pushNotificationsEnabled: true,
            createdAt: new Date(),
            updatedAt: new Date(),
            ...overrides,
        };
    }

    static createPushToken(userId: number, overrides: Partial<any> = {}) {
        return {
            id: Math.floor(Math.random() * 1000) + 1,
            userId,
            token: `fcm_token_${Math.random().toString(36).substring(7)}_${Date.now()}`,
            createdAt: new Date(),
            ...overrides,
        };
    }

    static createNotification(userId: number, overrides: Partial<any> = {}) {
        return {
            id: Math.floor(Math.random() * 1000) + 1,
            userId,
            notificationType: NotificationTypes.levelup,
            details: JSON.stringify({ level: 5, experience: 1000 }),
            read: false,
            createdAt: new Date(),
            ...overrides,
        };
    }

    static createNotificationPayload(overrides: Partial<any> = {}) {
        return {
            title: "Test Notification",
            body: "This is a test notification message",
            image: "https://example.com/test-image.png",
            data: {
                type: NotificationTypes.levelup,
                userId: "1",
                details: JSON.stringify({ level: 5 }),
            },
            ...overrides,
        };
    }
}

// Database test helpers
export class DatabaseTestHelper {
    constructor(private db: PrismaClient) {}

    async cleanupTestData() {
        // Clean up in reverse dependency order
        await this.db.push_token.deleteMany({
            where: {
                token: {
                    contains: "test_",
                },
            },
        });

        await this.db.notification.deleteMany({
            where: {
                details: {
                    contains: "test",
                },
            },
        });

        await this.db.user.deleteMany({
            where: {
                username: {
                    contains: "testuser_",
                },
            },
        });
    }

    async seedTestUser(userData?: Partial<any>) {
        const user = TestDataFactory.createUser(userData);
        return await this.db.user.create({
            data: user,
        });
    }

    async seedTestPushToken(userId: number, tokenData?: Partial<any>) {
        const token = TestDataFactory.createPushToken(userId, tokenData);
        return await this.db.push_token.create({
            data: token,
        });
    }

    async seedTestNotification(userId: number, notificationData?: Partial<any>) {
        const notification = TestDataFactory.createNotification(userId, notificationData);
        return await this.db.notification.create({
            data: notification,
        });
    }

    async createTestUserWithTokens(tokenCount: number = 1) {
        const user = await this.seedTestUser();
        const tokens = [];

        for (let i = 0; i < tokenCount; i++) {
            const token = await this.seedTestPushToken(user.id);
            tokens.push(token);
        }

        return { user, tokens };
    }
}

// Mock Firebase helpers
export class FirebaseMockHelper {
    static createMockFirebaseApp() {
        return {
            name: "test-app",
            options: {
                projectId: "test-project",
                clientEmail: "<EMAIL>",
            },
        };
    }

    static createMockMessaging() {
        return {
            send: vi.fn().mockResolvedValue("mock-message-id"),
            sendMulticast: vi.fn().mockResolvedValue({
                successCount: 1,
                failureCount: 0,
                responses: [{ success: true, messageId: "mock-message-id" }],
            }),
            sendAll: vi.fn().mockResolvedValue({
                successCount: 1,
                failureCount: 0,
                responses: [{ success: true, messageId: "mock-message-id" }],
            }),
        };
    }

    static createMockFirebaseError(code: string, message: string) {
        const error = new Error(message);
        (error as any).code = code;
        (error as any).errorInfo = {
            code,
            message,
        };
        return error;
    }
}

// API test helpers
export class ApiTestHelper {
    static createMockRequest(overrides: Partial<any> = {}) {
        return {
            body: {},
            params: {},
            query: {},
            headers: {},
            user: { id: 1 },
            ...overrides,
        };
    }

    static createMockResponse() {
        const res: any = {};
        res.status = vi.fn().mockReturnValue(res);
        res.json = vi.fn().mockReturnValue(res);
        res.send = vi.fn().mockReturnValue(res);
        res.end = vi.fn().mockReturnValue(res);
        return res;
    }

    static expectSuccessResponse(response: any, expectedData?: any) {
        expect(response.data).toBeDefined();
        if (expectedData) {
            expect(response.data).toEqual(expectedData);
        }
        expect(response.error).toBeUndefined();
    }

    static expectErrorResponse(response: any, expectedError?: string, expectedStatusCode?: number) {
        expect(response.error).toBeDefined();
        if (expectedError) {
            expect(response.error).toContain(expectedError);
        }
        if (expectedStatusCode) {
            expect(response.statusCode).toBe(expectedStatusCode);
        }
        expect(response.data).toBeUndefined();
    }
}

// Performance test helpers
export class PerformanceTestHelper {
    static async measureExecutionTime<T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> {
        const start = performance.now();
        const result = await fn();
        const end = performance.now();
        return {
            result,
            duration: end - start,
        };
    }

    static async measureMemoryUsage<T>(fn: () => Promise<T>): Promise<{ result: T; memoryUsed: number }> {
        const initialMemory = process.memoryUsage().heapUsed;
        const result = await fn();
        const finalMemory = process.memoryUsage().heapUsed;
        return {
            result,
            memoryUsed: finalMemory - initialMemory,
        };
    }

    static createLoadTestData(count: number) {
        return Array.from({ length: count }, (_, index) => ({
            id: index + 1,
            userId: index + 1,
            token: `load_test_token_${index}`,
        }));
    }
}

// Export all helpers
export { TestDataFactory, DatabaseTestHelper, FirebaseMockHelper, ApiTestHelper, PerformanceTestHelper };
