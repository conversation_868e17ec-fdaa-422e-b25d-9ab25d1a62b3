import { initializeApp } from "firebase/app";
import { getMessaging, onBackgroundMessage } from "firebase/messaging/sw";
import { clientsClaim } from "workbox-core";
import { precacheAndRoute } from "workbox-precaching";

precacheAndRoute(self.__WB_MANIFEST);

// Firebase configuration - should match frontend config
const firebaseConfig = {
    apiKey: "AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",
    authDomain: "chikara-academy.firebaseapp.com",
    projectId: "chikara-academy",
    storageBucket: "chikara-academy.appspot.com",
    messagingSenderId: "75175802639",
    appId: "1:75175802639:web:a1cc5e6073f185ec46707a",
};

// Initialize Firebase app and messaging
const firebaseApp = initializeApp(firebaseConfig);
const messaging = getMessaging(firebaseApp);

// Handle background messages
onBackgroundMessage(messaging, (payload) => {
    console.log("[firebase-messaging-sw.js] Received background message", payload);

    const { notification, data } = payload;

    if (notification) {
        const notificationTitle = notification.title || "Chikara Academy";
        const notificationOptions = {
            body: notification.body || "You have a new notification",
            icon: notification.image || "/logo192.png",
            badge: "/logo192.png",
            data: data || {},
            actions: [
                {
                    action: "open",
                    title: "Open Game",
                },
                {
                    action: "dismiss",
                    title: "Dismiss",
                },
            ],
            requireInteraction: false,
            silent: false,
        };

        self.registration.showNotification(notificationTitle, notificationOptions);
    }
});

// Handle notification clicks
self.addEventListener("notificationclick", (event) => {
    console.log("[firebase-messaging-sw.js] Notification click received.");

    event.notification.close();

    if (event.action === "dismiss") {
        return;
    }

    // Open the app when notification is clicked
    event.waitUntil(
        clients.matchAll({ type: "window", includeUncontrolled: true }).then((clientList) => {
            // Check if app is already open
            for (const client of clientList) {
                if (client.url.includes(self.location.origin) && "focus" in client) {
                    return client.focus();
                }
            }

            // Open new window if app is not open
            if (clients.openWindow) {
                return clients.openWindow("/");
            }
        })
    );
});

self.skipWaiting();
clientsClaim();
