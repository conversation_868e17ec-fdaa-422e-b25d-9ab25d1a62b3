import { fetchGameConfig } from "@/app/fetchGameConfig";
// import { sendTokenToServer } from "@/app/firebase";
import { useAuthStore, useNormalStore, usePersistStore } from "@/app/store/stores";
import ErrorBoundary from "@/components/Layout/ErrorBoundary";
import MaintenanceMode from "@/components/MaintenanceMode";
import LoadingSpinner from "@/components/Spinners/Spinner";
import ToastManager from "@/components/ToastManager";
import Tooltips from "@/components/Tooltips";
import {
    AllCommunityModule,
    ClientSideRowModelModule,
    ModuleRegistry,
    provideGlobalGridOptions,
} from "ag-grid-community";
import posthog from "posthog-js";
import { Suspense, useEffect } from "react";
import { Outlet, useLocation } from "react-router-dom";
import SocketManager from "./SocketManager";

// ---------AG GRID---------
ModuleRegistry.registerModules([ClientSideRowModelModule, AllCommunityModule]);
provideGlobalGridOptions({ theme: "legacy" });
// ----------------------------

function App() {
    const location = useLocation();
    const { refreshPagePrompt, setRefreshPagePrompt, setShowDailyModal, messagingToken, isInMaintenance } =
        useNormalStore();

    const { colourTheme, gameConfig } = usePersistStore();
    const authed = useAuthStore((state) => state.authed);

    useEffect(() => {
        document.body.classList.add(colourTheme);
    }, [colourTheme]);

    useEffect(() => {
        posthog.capture("$pageview");
    }, [location.pathname]);

    useEffect(() => {
        if (!gameConfig) {
            console.log("Fetching initial config..");
            fetchGameConfig();
        }
        if (refreshPagePrompt) {
            setRefreshPagePrompt(false);
        }

        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // useEffect(() => {
    //     if (authed && messagingToken) {
    //         sendTokenToServer(messagingToken);
    //     }
    // }, [authed, messagingToken]);

    // If the app is in maintenance mode, show the maintenance component
    if (isInMaintenance) {
        return <MaintenanceMode />;
    }

    return (
        <>
            <SocketManager />
            <ErrorBoundary>
                <Suspense
                    fallback={
                        <div className="size-full flex items-center justify-center">
                            <LoadingSpinner />
                        </div>
                    }
                >
                    <Outlet />
                </Suspense>
            </ErrorBoundary>
            <ToastManager />
            <Tooltips />
        </>
    );
}

export default App;
