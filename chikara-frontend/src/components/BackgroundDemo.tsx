import { cn } from "@/lib/utils";
import { AnimatedGridPattern } from "@/components/ui/animated-grid-pattern";
import { DotPattern } from "@/components/ui/dot-pattern";
import { Ripple } from "@/components/ui/ripple";
import { RetroGrid } from "@/components/ui/retro-grid";
import { FlickeringGrid } from "@/components/ui/flickering-grid";

export function BackgroundDemo() {
    return (
        <div className="relative min-h-screen bg-gray-900 overflow-hidden">
            {/* Enhanced Background Patterns */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {/* Animated Grid Pattern for dynamic effect */}
                <AnimatedGridPattern
                    numSquares={30}
                    maxOpacity={0.1}
                    duration={3}
                    repeatDelay={1}
                    className={cn(
                        "[mask-image:radial-gradient(500px_circle_at_center,white,transparent)]",
                        "inset-x-0 inset-y-[-30%] h-[200%] skew-y-12 fill-purple-400/30 stroke-purple-400/30"
                    )}
                />
                
                {/* <PERSON> Pattern for subtle texture */}
                <DotPattern
                    width={20}
                    height={20}
                    cx={1}
                    cy={1}
                    cr={1}
                    className={cn(
                        "[mask-image:radial-gradient(400px_circle_at_center,white,transparent)]",
                        "text-purple-400/20"
                    )}
                />
                
                {/* Retro Grid for gaming aesthetic */}
                <RetroGrid
                    angle={65}
                    cellSize={80}
                    opacity={0.1}
                    lightLineColor="rgb(147, 51, 234)"
                    darkLineColor="rgb(147, 51, 234)"
                    className="[mask-image:radial-gradient(600px_circle_at_center,white,transparent)]"
                />
                
                {/* Flickering Grid for dynamic energy */}
                <FlickeringGrid
                    className="absolute inset-0 z-0 size-full [mask-image:radial-gradient(400px_circle_at_center,white,transparent)]"
                    squareSize={4}
                    gridGap={6}
                    color="#9333ea"
                    maxOpacity={0.3}
                    flickerChance={0.1}
                />
                
                {/* Ripple effect for central focus */}
                <Ripple
                    mainCircleSize={150}
                    mainCircleOpacity={0.1}
                    numCircles={6}
                    className="[mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
                />
                
                {/* Gradient overlays for depth */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-blue-500/5" />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/10 via-transparent to-transparent" />
            </div>

            {/* Content */}
            <div className="relative z-10 flex items-center justify-center min-h-screen">
                <div className="text-center">
                    <h1 className="text-6xl font-bold text-white mb-4">
                        Chikara Academy
                    </h1>
                    <p className="text-xl text-purple-300">
                        Enhanced Background Patterns Demo
                    </p>
                    <div className="mt-8 grid grid-cols-2 gap-4 max-w-md mx-auto">
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                            <h3 className="text-white font-semibold">Animated Grid</h3>
                            <p className="text-purple-200 text-sm">Dynamic squares</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                            <h3 className="text-white font-semibold">Dot Pattern</h3>
                            <p className="text-purple-200 text-sm">Subtle texture</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                            <h3 className="text-white font-semibold">Retro Grid</h3>
                            <p className="text-purple-200 text-sm">Gaming aesthetic</p>
                        </div>
                        <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                            <h3 className="text-white font-semibold">Ripple Effect</h3>
                            <p className="text-purple-200 text-sm">Central focus</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}
