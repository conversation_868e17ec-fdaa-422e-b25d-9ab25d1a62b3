/**
 * Notification Settings Component
 * Example component for managing Firebase push notifications
 */
import React, { useState, useEffect } from "react";
import { useFirebaseNotifications } from "@/hooks/useFirebaseNotifications";
import { client } from "@/lib/orpc";

interface NotificationSettingsProps {
    className?: string;
}

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({ className = "" }) => {
    const { permission, token, isLoading, error, requestPermission, removeToken } = useFirebaseNotifications();

    const [pushEnabled, setPushEnabled] = useState<boolean>(false);
    const [settingsLoading, setSettingsLoading] = useState(false);
    const [settingsError, setSettingsError] = useState<string | null>(null);

    // Load current push notification settings
    useEffect(() => {
        const loadSettings = async () => {
            try {
                const response = await client.notification.getPushNotificationSettings();
                if (response.data) {
                    setPushEnabled(response.data.pushNotificationsEnabled);
                }
            } catch (err) {
                console.error("Failed to load notification settings:", err);
            }
        };

        loadSettings();
    }, []);

    const handleEnableNotifications = async () => {
        try {
            const result = await requestPermission();

            if (result.permission === "granted" && result.token) {
                // Update server settings
                await updatePushSettings(true);
            } else if (result.error) {
                setSettingsError(result.error);
            }
        } catch (err) {
            setSettingsError(err instanceof Error ? err.message : "Failed to enable notifications");
        }
    };

    const handleDisableNotifications = async () => {
        try {
            // Remove token from server
            if (token) {
                await removeToken();
            }

            // Update server settings
            await updatePushSettings(false);
        } catch (err) {
            setSettingsError(err instanceof Error ? err.message : "Failed to disable notifications");
        }
    };

    const updatePushSettings = async (enabled: boolean) => {
        setSettingsLoading(true);
        setSettingsError(null);

        try {
            await client.notification.updatePushNotificationSettings({ pushEnabled: enabled });
            setPushEnabled(enabled);
        } catch (err) {
            setSettingsError(err instanceof Error ? err.message : "Failed to update settings");
        } finally {
            setSettingsLoading(false);
        }
    };

    const getPermissionStatus = () => {
        switch (permission) {
            case "granted":
                return { text: "Granted", color: "text-green-600" };
            case "denied":
                return { text: "Denied", color: "text-red-600" };
            default:
                return { text: "Not requested", color: "text-yellow-600" };
        }
    };

    const permissionStatus = getPermissionStatus();

    return (
        <div className={`bg-white rounded-lg shadow p-6 ${className}`}>
            <h3 className="text-lg font-semibold mb-4">Push Notifications</h3>

            {/* Status Information */}
            <div className="space-y-2 mb-6">
                <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Browser Permission:</span>
                    <span className={`text-sm font-medium ${permissionStatus.color}`}>{permissionStatus.text}</span>
                </div>

                <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Push Notifications:</span>
                    <span className={`text-sm font-medium ${pushEnabled ? "text-green-600" : "text-gray-600"}`}>
                        {pushEnabled ? "Enabled" : "Disabled"}
                    </span>
                </div>

                <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">Token Status:</span>
                    <span className={`text-sm font-medium ${token ? "text-green-600" : "text-gray-600"}`}>
                        {token ? "Active" : "None"}
                    </span>
                </div>
            </div>

            {/* Error Display */}
            {(error || settingsError) && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-4">
                    <p className="text-sm text-red-600">{error || settingsError}</p>
                </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
                {permission !== "granted" && (
                    <button
                        onClick={handleEnableNotifications}
                        disabled={isLoading || settingsLoading}
                        className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading ? "Requesting Permission..." : "Enable Push Notifications"}
                    </button>
                )}

                {permission === "granted" && !pushEnabled && (
                    <button
                        onClick={() => updatePushSettings(true)}
                        disabled={settingsLoading}
                        className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {settingsLoading ? "Updating..." : "Enable Notifications"}
                    </button>
                )}

                {permission === "granted" && pushEnabled && (
                    <button
                        onClick={handleDisableNotifications}
                        disabled={isLoading || settingsLoading}
                        className="w-full bg-red-600 text-white py-2 px-4 rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        {isLoading || settingsLoading ? "Updating..." : "Disable Notifications"}
                    </button>
                )}
            </div>

            {/* Help Text */}
            <div className="mt-4 text-xs text-gray-500">
                <p>
                    Push notifications will alert you about important game events even when the game is closed. You can
                    change this setting at any time.
                </p>
                {permission === "denied" && (
                    <p className="mt-2 text-red-500">
                        Notifications are blocked in your browser. Please enable them in your browser settings and
                        refresh the page to use push notifications.
                    </p>
                )}
            </div>
        </div>
    );
};
