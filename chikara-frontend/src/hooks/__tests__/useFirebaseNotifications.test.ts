/**
 * Tests for useFirebaseNotifications hook
 */
import { describe, it, expect, beforeEach, vi } from "vitest";
import { renderHook, act, waitFor } from "@testing-library/react";
import { useFirebaseNotifications } from "../useFirebaseNotifications";
import {
    FrontendTestDataFactory,
    FirebaseMockService,
    NotificationMockService,
    ServiceWorkerMockService,
    AsyncTestUtils,
    ErrorSimulator,
} from "../test/testUtils";

// Mock the Firebase messaging service
const mockFirebaseMessagingService = {
    initialize: vi.fn(),
    requestPermissionAndGetToken: vi.fn(),
    removeTokenFromServer: vi.fn(),
    getCurrentToken: vi.fn(),
    isServiceInitialized: vi.fn(),
};

vi.mock("@/lib/firebase", () => ({
    firebaseMessagingService: mockFirebaseMessagingService,
}));

// Mock the normal store
const mockNormalStore = {
    getState: vi.fn(() => ({
        messagingToken: "",
        setMessagingToken: vi.fn(),
    })),
};

vi.mock("@/app/store/normalStore", () => ({
    normalStore: mockNormalStore,
}));

describe("useFirebaseNotifications", () => {
    beforeEach(() => {
        vi.clearAllMocks();
        NotificationMockService.setupNotificationMocks("default");
        ServiceWorkerMockService.setupServiceWorkerMocks();

        // Setup default mock implementations
        mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
        mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(false);
        mockFirebaseMessagingService.getCurrentToken.mockReturnValue(null);
        mockNormalStore.getState.mockReturnValue({
            messagingToken: "",
            setMessagingToken: vi.fn(),
        });
    });

    describe("Initialization", () => {
        it("should initialize with default state", () => {
            const { result } = renderHook(() => useFirebaseNotifications());

            expect(result.current.isInitialized).toBe(false);
            expect(result.current.permission).toBe("default");
            expect(result.current.token).toBeNull();
            expect(result.current.isLoading).toBe(false);
            expect(result.current.error).toBeNull();
        });

        it("should initialize Firebase messaging service on mount", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);

            renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(mockFirebaseMessagingService.initialize).toHaveBeenCalled();
            });
        });

        it("should set initialized state after successful initialization", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });
        });

        it("should handle initialization errors", async () => {
            const initError = new Error("Initialization failed");
            mockFirebaseMessagingService.initialize.mockRejectedValue(initError);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.error).toBe("Initialization failed");
                expect(result.current.isInitialized).toBe(false);
            });
        });

        it("should load stored token from store", async () => {
            mockNormalStore.getState.mockReturnValue({
                messagingToken: "stored-token",
                setMessagingToken: vi.fn(),
            });
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.token).toBe("stored-token");
            });
        });
    });

    describe("Permission Management", () => {
        beforeEach(async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);
        });

        it("should detect current notification permission", async () => {
            NotificationMockService.setPermission("granted");

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.permission).toBe("granted");
            });
        });

        it("should request permission successfully", async () => {
            const mockSetMessagingToken = vi.fn();
            mockNormalStore.getState.mockReturnValue({
                messagingToken: "",
                setMessagingToken: mockSetMessagingToken,
            });

            const mockResult = FrontendTestDataFactory.createMockPermissionResult({
                permission: "granted",
                token: "new-token",
            });
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockResolvedValue(mockResult);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            await act(async () => {
                const permissionResult = await result.current.requestPermission();
                expect(permissionResult.permission).toBe("granted");
                expect(permissionResult.token).toBe("new-token");
            });

            expect(result.current.permission).toBe("granted");
            expect(result.current.token).toBe("new-token");
            expect(mockSetMessagingToken).toHaveBeenCalledWith("new-token");
        });

        it("should handle permission denial", async () => {
            const mockResult = FrontendTestDataFactory.createMockPermissionResult({
                permission: "denied",
                error: "Permission denied by user",
            });
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockResolvedValue(mockResult);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            await act(async () => {
                const permissionResult = await result.current.requestPermission();
                expect(permissionResult.permission).toBe("denied");
                expect(permissionResult.error).toBe("Permission denied by user");
            });

            expect(result.current.permission).toBe("denied");
            expect(result.current.error).toBe("Permission denied by user");
        });

        it("should handle permission request when not initialized", async () => {
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(false);

            const { result } = renderHook(() => useFirebaseNotifications());

            await act(async () => {
                const permissionResult = await result.current.requestPermission();
                expect(permissionResult.permission).toBe("denied");
                expect(permissionResult.error).toBe("Firebase messaging not initialized");
            });
        });

        it("should show loading state during permission request", async () => {
            let resolvePermission: (value: any) => void;
            const permissionPromise = new Promise((resolve) => {
                resolvePermission = resolve;
            });
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockReturnValue(permissionPromise);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            act(() => {
                result.current.requestPermission();
            });

            expect(result.current.isLoading).toBe(true);

            await act(async () => {
                resolvePermission!(FrontendTestDataFactory.createMockPermissionResult());
                await AsyncTestUtils.waitForNextTick();
            });

            expect(result.current.isLoading).toBe(false);
        });
    });

    describe("Token Management", () => {
        beforeEach(async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);
        });

        it("should remove token successfully", async () => {
            const mockSetMessagingToken = vi.fn();
            mockNormalStore.getState.mockReturnValue({
                messagingToken: "current-token",
                setMessagingToken: mockSetMessagingToken,
            });
            mockFirebaseMessagingService.removeTokenFromServer.mockResolvedValue(true);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.token).toBe("current-token");
            });

            await act(async () => {
                const success = await result.current.removeToken();
                expect(success).toBe(true);
            });

            expect(result.current.token).toBeNull();
            expect(mockSetMessagingToken).toHaveBeenCalledWith("");
            expect(mockFirebaseMessagingService.removeTokenFromServer).toHaveBeenCalledWith("current-token");
        });

        it("should handle token removal when no token exists", async () => {
            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            await act(async () => {
                const success = await result.current.removeToken();
                expect(success).toBe(false);
            });

            expect(mockFirebaseMessagingService.removeTokenFromServer).not.toHaveBeenCalled();
        });

        it("should handle token removal errors", async () => {
            mockNormalStore.getState.mockReturnValue({
                messagingToken: "current-token",
                setMessagingToken: vi.fn(),
            });
            mockFirebaseMessagingService.removeTokenFromServer.mockRejectedValue(new Error("Server error"));

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.token).toBe("current-token");
            });

            await act(async () => {
                const success = await result.current.removeToken();
                expect(success).toBe(false);
            });

            expect(result.current.error).toBe("Server error");
        });

        it("should show loading state during token removal", async () => {
            mockNormalStore.getState.mockReturnValue({
                messagingToken: "current-token",
                setMessagingToken: vi.fn(),
            });

            let resolveRemoval: (value: boolean) => void;
            const removalPromise = new Promise<boolean>((resolve) => {
                resolveRemoval = resolve;
            });
            mockFirebaseMessagingService.removeTokenFromServer.mockReturnValue(removalPromise);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.token).toBe("current-token");
            });

            act(() => {
                result.current.removeToken();
            });

            expect(result.current.isLoading).toBe(true);

            await act(async () => {
                resolveRemoval!(true);
                await AsyncTestUtils.waitForNextTick();
            });

            expect(result.current.isLoading).toBe(false);
        });
    });

    describe("Message Handling", () => {
        beforeEach(async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);
        });

        it("should set up firebase message event listener", async () => {
            const addEventListenerSpy = vi.spyOn(window, "addEventListener");

            renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(addEventListenerSpy).toHaveBeenCalledWith("firebase-message", expect.any(Function));
            });
        });

        it("should clean up event listener on unmount", async () => {
            const removeEventListenerSpy = vi.spyOn(window, "removeEventListener");

            const { unmount } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(window.addEventListener).toHaveBeenCalled();
            });

            unmount();

            expect(removeEventListenerSpy).toHaveBeenCalledWith("firebase-message", expect.any(Function));
        });

        it("should handle firebase messages", async () => {
            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            const mockPayload = FrontendTestDataFactory.createMockNotificationPayload();

            act(() => {
                window.dispatchEvent(
                    new CustomEvent("firebase-message", {
                        detail: mockPayload,
                    })
                );
            });

            // The hook should handle the message (implementation depends on your requirements)
            // This test verifies the event listener is working
        });
    });

    describe("Error Handling", () => {
        it("should handle network errors gracefully", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockRejectedValue(
                ErrorSimulator.createNetworkError("Network unavailable")
            );

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            await act(async () => {
                await result.current.requestPermission();
            });

            expect(result.current.error).toBe("Network unavailable");
        });

        it("should handle Firebase errors gracefully", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockRejectedValue(
                ErrorSimulator.createFirebaseError("messaging/invalid-vapid-key", "Invalid VAPID key")
            );

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            await act(async () => {
                await result.current.requestPermission();
            });

            expect(result.current.error).toBe("Invalid VAPID key");
        });

        it("should clear errors on successful operations", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            // First, cause an error
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockRejectedValue(new Error("Test error"));

            await act(async () => {
                await result.current.requestPermission();
            });

            expect(result.current.error).toBe("Test error");

            // Then, perform successful operation
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockResolvedValue(
                FrontendTestDataFactory.createMockPermissionResult()
            );

            await act(async () => {
                await result.current.requestPermission();
            });

            expect(result.current.error).toBeNull();
        });
    });

    describe("State Consistency", () => {
        it("should maintain consistent state across re-renders", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);

            const { result, rerender } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            const initialState = { ...result.current };

            rerender();

            expect(result.current.isInitialized).toBe(initialState.isInitialized);
            expect(result.current.permission).toBe(initialState.permission);
            expect(result.current.token).toBe(initialState.token);
        });

        it("should handle rapid successive calls gracefully", async () => {
            mockFirebaseMessagingService.initialize.mockResolvedValue(undefined);
            mockFirebaseMessagingService.isServiceInitialized.mockReturnValue(true);
            mockFirebaseMessagingService.requestPermissionAndGetToken.mockResolvedValue(
                FrontendTestDataFactory.createMockPermissionResult()
            );

            const { result } = renderHook(() => useFirebaseNotifications());

            await waitFor(() => {
                expect(result.current.isInitialized).toBe(true);
            });

            // Make multiple rapid calls
            const promises = [
                result.current.requestPermission(),
                result.current.requestPermission(),
                result.current.requestPermission(),
            ];

            await act(async () => {
                await Promise.all(promises);
            });

            // Should handle gracefully without errors
            expect(result.current.error).toBeNull();
        });
    });
});
