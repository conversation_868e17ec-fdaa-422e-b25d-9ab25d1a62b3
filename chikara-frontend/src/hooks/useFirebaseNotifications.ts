/**
 * React hook for managing Firebase notifications
 */
import { useEffect, useState, useCallback } from "react";
import { firebaseMessagingService, NotificationPermissionResult } from "@/lib/firebase";
import { normalStore } from "@/app/store/normalStore";

interface UseFirebaseNotificationsReturn {
    isInitialized: boolean;
    permission: NotificationPermission;
    token: string | null;
    isLoading: boolean;
    error: string | null;
    requestPermission: () => Promise<NotificationPermissionResult>;
    removeToken: () => Promise<boolean>;
}

export const useFirebaseNotifications = (): UseFirebaseNotificationsReturn => {
    const [isInitialized, setIsInitialized] = useState(false);
    const [permission, setPermission] = useState<NotificationPermission>("default");
    const [token, setToken] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    // Initialize Firebase messaging service
    useEffect(() => {
        const initializeService = async () => {
            try {
                await firebaseMessagingService.initialize();
                setIsInitialized(true);

                // Get current permission status
                if ("Notification" in window) {
                    setPermission(Notification.permission);
                }

                // Get stored token from store
                const { messagingToken } = normalStore.getState();
                if (messagingToken) {
                    setToken(messagingToken);
                }
            } catch (err) {
                console.error("Failed to initialize Firebase messaging:", err);
                setError(err instanceof Error ? err.message : "Failed to initialize");
            }
        };

        initializeService();
    }, []);

    // Listen for foreground messages
    useEffect(() => {
        const handleFirebaseMessage = (event: CustomEvent) => {
            console.log("Received Firebase message:", event.detail);
            // Handle the message in your app (update UI, show toast, etc.)
        };

        window.addEventListener("firebase-message", handleFirebaseMessage as EventListener);

        return () => {
            window.removeEventListener("firebase-message", handleFirebaseMessage as EventListener);
        };
    }, []);

    const requestPermission = useCallback(async (): Promise<NotificationPermissionResult> => {
        if (!isInitialized) {
            return {
                permission: "denied",
                error: "Firebase messaging not initialized",
            };
        }

        setIsLoading(true);
        setError(null);

        try {
            // Get service worker registration
            let registration: ServiceWorkerRegistration | undefined;

            if ("serviceWorker" in navigator) {
                registration = await navigator.serviceWorker.ready;
            }

            const result = await firebaseMessagingService.requestPermissionAndGetToken(registration);

            setPermission(result.permission);

            if (result.token) {
                setToken(result.token);
                // Update store
                const { setMessagingToken } = normalStore.getState();
                setMessagingToken(result.token);
            }

            if (result.error) {
                setError(result.error);
            }

            return result;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Unknown error";
            setError(errorMessage);
            return {
                permission: "denied",
                error: errorMessage,
            };
        } finally {
            setIsLoading(false);
        }
    }, [isInitialized]);

    const removeToken = useCallback(async (): Promise<boolean> => {
        if (!token) {
            return false;
        }

        setIsLoading(true);
        setError(null);

        try {
            const success = await firebaseMessagingService.removeTokenFromServer(token);

            if (success) {
                setToken(null);
                // Update store
                const { setMessagingToken } = normalStore.getState();
                setMessagingToken("");
            }

            return success;
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : "Failed to remove token";
            setError(errorMessage);
            return false;
        } finally {
            setIsLoading(false);
        }
    }, [token]);

    return {
        isInitialized,
        permission,
        token,
        isLoading,
        error,
        requestPermission,
        removeToken,
    };
};
