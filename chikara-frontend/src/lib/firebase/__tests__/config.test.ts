/**
 * Tests for Firebase configuration
 */
import { describe, it, expect, beforeEach, vi } from "vitest";
import { initializeFirebase, getFirebaseMessaging, VAPID_KEY, firebaseConfig } from "../config";

// Mock Firebase modules
vi.mock("firebase/app", () => ({
    initializeApp: vi.fn(() => ({ name: "test-app" })),
}));

vi.mock("firebase/messaging", () => ({
    getMessaging: vi.fn(() => ({ app: { name: "test-app" } })),
}));

describe("Firebase Configuration", () => {
    beforeEach(() => {
        vi.clearAllMocks();
    });

    describe("firebaseConfig", () => {
        it("should have all required configuration properties", () => {
            expect(firebaseConfig).toHaveProperty("apiKey");
            expect(firebaseConfig).toHaveProperty("authDomain");
            expect(firebaseConfig).toHaveProperty("projectId");
            expect(firebaseConfig).toHaveProperty("storageBucket");
            expect(firebaseConfig).toHaveProperty("messagingSenderId");
            expect(firebaseConfig).toHaveProperty("appId");
        });

        it("should use environment variables when available", () => {
            // Environment variables are mocked in test setup
            expect(firebaseConfig.apiKey).toBe("test-api-key");
            expect(firebaseConfig.projectId).toBe("test-project");
        });

        it("should have fallback values when environment variables are not set", () => {
            // Test that fallback values exist (from the actual implementation)
            expect(firebaseConfig.apiKey).toBeTruthy();
            expect(firebaseConfig.projectId).toBeTruthy();
            expect(firebaseConfig.authDomain).toBeTruthy();
        });
    });

    describe("VAPID_KEY", () => {
        it("should be defined", () => {
            expect(VAPID_KEY).toBeDefined();
            expect(typeof VAPID_KEY).toBe("string");
            expect(VAPID_KEY.length).toBeGreaterThan(0);
        });

        it("should use environment variable when available", () => {
            expect(VAPID_KEY).toBe("test-vapid-key");
        });
    });

    describe("initializeFirebase", () => {
        it("should initialize Firebase app successfully", () => {
            const { initializeApp } = require("firebase/app");

            const app = initializeFirebase();

            expect(initializeApp).toHaveBeenCalledWith(firebaseConfig);
            expect(app).toBeDefined();
            expect(app.name).toBe("test-app");
        });

        it("should return the same app instance on subsequent calls", () => {
            const app1 = initializeFirebase();
            const app2 = initializeFirebase();

            expect(app1).toBe(app2);
        });

        it("should only call initializeApp once for multiple calls", () => {
            const { initializeApp } = require("firebase/app");

            initializeFirebase();
            initializeFirebase();
            initializeFirebase();

            expect(initializeApp).toHaveBeenCalledTimes(1);
        });
    });

    describe("getFirebaseMessaging", () => {
        it("should get messaging instance successfully", () => {
            const { getMessaging } = require("firebase/messaging");

            const messaging = getFirebaseMessaging();

            expect(getMessaging).toHaveBeenCalled();
            expect(messaging).toBeDefined();
        });

        it("should initialize Firebase app if not already initialized", () => {
            const { initializeApp } = require("firebase/app");
            const { getMessaging } = require("firebase/messaging");

            getFirebaseMessaging();

            expect(initializeApp).toHaveBeenCalled();
            expect(getMessaging).toHaveBeenCalled();
        });

        it("should return the same messaging instance on subsequent calls", () => {
            const messaging1 = getFirebaseMessaging();
            const messaging2 = getFirebaseMessaging();

            expect(messaging1).toBe(messaging2);
        });
    });

    describe("Error Handling", () => {
        it("should handle Firebase initialization errors gracefully", () => {
            const { initializeApp } = require("firebase/app");
            initializeApp.mockImplementationOnce(() => {
                throw new Error("Firebase initialization failed");
            });

            expect(() => initializeFirebase()).toThrow("Firebase initialization failed");
        });

        it("should handle messaging initialization errors gracefully", () => {
            const { getMessaging } = require("firebase/messaging");
            getMessaging.mockImplementationOnce(() => {
                throw new Error("Messaging initialization failed");
            });

            expect(() => getFirebaseMessaging()).toThrow("Messaging initialization failed");
        });
    });

    describe("Configuration Validation", () => {
        it("should validate required configuration properties", () => {
            const requiredProps = ["apiKey", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId"];

            requiredProps.forEach((prop) => {
                expect(firebaseConfig[prop]).toBeDefined();
                expect(firebaseConfig[prop]).not.toBe("");
            });
        });

        it("should have valid project ID format", () => {
            expect(firebaseConfig.projectId).toMatch(/^[a-z0-9-]+$/);
        });

        it("should have valid auth domain format", () => {
            expect(firebaseConfig.authDomain).toMatch(/\.firebaseapp\.com$/);
        });

        it("should have valid storage bucket format", () => {
            expect(firebaseConfig.storageBucket).toMatch(/\.appspot\.com$/);
        });

        it("should have numeric messaging sender ID", () => {
            expect(firebaseConfig.messagingSenderId).toMatch(/^\d+$/);
        });
    });

    describe("Environment-based Configuration", () => {
        it("should prioritize environment variables over defaults", () => {
            // Since we mock environment variables in test setup
            expect(firebaseConfig.apiKey).toBe("test-api-key");
            expect(firebaseConfig.projectId).toBe("test-project");
        });

        it("should handle missing environment variables gracefully", () => {
            // Test that the configuration still works with fallback values
            expect(firebaseConfig).toBeDefined();
            expect(Object.keys(firebaseConfig).length).toBeGreaterThan(0);
        });
    });
});
