/**
 * Tests for Firebase messaging service
 */
import { describe, it, expect, beforeEach, vi } from "vitest";
import { firebaseMessagingService } from "../messaging";
import {
    FrontendTestDataFactory,
    FirebaseMockService,
    NotificationMockService,
    ServiceWorkerMockService,
    ErrorSimulator,
} from "../../test/testUtils";

// Mock the config module
vi.mock("../config", () => ({
    getFirebaseMessaging: vi.fn(() => FirebaseMockService.mockMessaging),
    VAPID_KEY: "test-vapid-key",
}));

// Mock ORPC client
const mockORPCClient = {
    notification: {
        saveFCMToken: vi.fn(),
        removeFCMToken: vi.fn(),
    },
};

vi.mock("@/lib/orpc", () => ({
    client: mockORPCClient,
}));

describe("FirebaseMessagingService", () => {
    beforeEach(() => {
        vi.clearAllMocks();
        FirebaseMockService.setupFirebaseMocks();
        NotificationMockService.setupNotificationMocks("default");
        ServiceWorkerMockService.setupServiceWorkerMocks();

        // Reset service state
        (firebaseMessagingService as any).isInitialized = false;
        (firebaseMessagingService as any).currentToken = null;
    });

    describe("initialize", () => {
        it("should initialize successfully", async () => {
            await firebaseMessagingService.initialize();

            expect(firebaseMessagingService.isServiceInitialized()).toBe(true);
        });

        it("should not initialize twice", async () => {
            await firebaseMessagingService.initialize();
            await firebaseMessagingService.initialize();

            expect(firebaseMessagingService.isServiceInitialized()).toBe(true);
        });

        it("should set up foreground message listener", async () => {
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();

            await firebaseMessagingService.initialize();

            expect(mockMessaging.onMessage).toHaveBeenCalled();
        });

        it("should handle initialization errors", async () => {
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.onMessage.mockImplementationOnce(() => {
                throw new Error("Initialization failed");
            });

            await expect(firebaseMessagingService.initialize()).rejects.toThrow("Initialization failed");
        });
    });

    describe("requestPermissionAndGetToken", () => {
        beforeEach(async () => {
            await firebaseMessagingService.initialize();
        });

        it("should request permission and get token successfully", async () => {
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.getToken.mockResolvedValue("test-token");
            mockORPCClient.notification.saveFCMToken.mockResolvedValue({ data: "success" });

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.permission).toBe("granted");
            expect(result.token).toBe("test-token");
            expect(result.error).toBeUndefined();
            expect(mockORPCClient.notification.saveFCMToken).toHaveBeenCalledWith({ token: "test-token" });
        });

        it("should handle denied permission", async () => {
            NotificationMockService.setPermission("denied");

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.permission).toBe("denied");
            expect(result.token).toBeUndefined();
            expect(result.error).toBe("Notifications denied by user");
        });

        it("should handle unsupported browser", async () => {
            // Mock unsupported browser
            Object.defineProperty(window, "Notification", {
                value: undefined,
                writable: true,
            });

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.permission).toBe("denied");
            expect(result.error).toBe("Notifications not supported in this browser");
        });

        it("should handle token generation failure", async () => {
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.getToken.mockResolvedValue(null);

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.permission).toBe("granted");
            expect(result.token).toBeUndefined();
            expect(result.error).toBe("Failed to generate FCM token");
        });

        it("should handle server registration failure", async () => {
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.getToken.mockResolvedValue("test-token");
            mockORPCClient.notification.saveFCMToken.mockRejectedValue(new Error("Server error"));

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.permission).toBe("granted");
            expect(result.token).toBe("test-token");
            expect(result.error).toBe("Server error");
        });

        it("should use service worker registration when provided", async () => {
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            const mockRegistration = ServiceWorkerMockService.setupServiceWorkerMocks();

            await firebaseMessagingService.requestPermissionAndGetToken(mockRegistration);

            expect(mockMessaging.getToken).toHaveBeenCalledWith({
                vapidKey: "test-vapid-key",
                serviceWorkerRegistration: mockRegistration,
            });
        });
    });

    describe("removeTokenFromServer", () => {
        beforeEach(async () => {
            await firebaseMessagingService.initialize();
        });

        it("should remove token successfully", async () => {
            // Set up service with a token
            (firebaseMessagingService as any).currentToken = "test-token";
            mockORPCClient.notification.removeFCMToken.mockResolvedValue({ data: "success" });

            const result = await firebaseMessagingService.removeTokenFromServer();

            expect(result).toBe(true);
            expect(mockORPCClient.notification.removeFCMToken).toHaveBeenCalledWith({ token: "test-token" });
            expect(firebaseMessagingService.getCurrentToken()).toBeNull();
        });

        it("should remove specific token", async () => {
            mockORPCClient.notification.removeFCMToken.mockResolvedValue({ data: "success" });

            const result = await firebaseMessagingService.removeTokenFromServer("specific-token");

            expect(result).toBe(true);
            expect(mockORPCClient.notification.removeFCMToken).toHaveBeenCalledWith({ token: "specific-token" });
        });

        it("should handle no token to remove", async () => {
            const result = await firebaseMessagingService.removeTokenFromServer();

            expect(result).toBe(false);
            expect(mockORPCClient.notification.removeFCMToken).not.toHaveBeenCalled();
        });

        it("should handle server error", async () => {
            (firebaseMessagingService as any).currentToken = "test-token";
            mockORPCClient.notification.removeFCMToken.mockRejectedValue(new Error("Server error"));

            const result = await firebaseMessagingService.removeTokenFromServer();

            expect(result).toBe(false);
        });
    });

    describe("foreground message handling", () => {
        beforeEach(async () => {
            await firebaseMessagingService.initialize();
        });

        it("should handle foreground messages", async () => {
            const mockPayload = FrontendTestDataFactory.createMockNotificationPayload();
            const mockNotification = NotificationMockService.setupNotificationMocks("granted");

            // Simulate receiving a message
            const messageCallback = FirebaseMockService.mockMessaging.onMessage.mock.calls[0][1];
            messageCallback(mockPayload);

            // Should create browser notification
            expect(mockNotification).toHaveBeenCalledWith(
                "Test Notification",
                expect.objectContaining({
                    body: "This is a test notification",
                    icon: "/test-icon.png",
                })
            );
        });

        it("should emit custom event for foreground messages", async () => {
            const mockPayload = FrontendTestDataFactory.createMockNotificationPayload();
            let eventFired = false;

            window.addEventListener("firebase-message", (event) => {
                eventFired = true;
                expect((event as CustomEvent).detail).toEqual(mockPayload);
            });

            // Simulate receiving a message
            const messageCallback = FirebaseMockService.mockMessaging.onMessage.mock.calls[0][1];
            messageCallback(mockPayload);

            expect(eventFired).toBe(true);
        });

        it("should not create browser notification when permission denied", async () => {
            const mockPayload = FrontendTestDataFactory.createMockNotificationPayload();
            const mockNotification = NotificationMockService.setupNotificationMocks("denied");

            // Simulate receiving a message
            const messageCallback = FirebaseMockService.mockMessaging.onMessage.mock.calls[0][1];
            messageCallback(mockPayload);

            expect(mockNotification).not.toHaveBeenCalled();
        });
    });

    describe("getCurrentToken", () => {
        it("should return null initially", () => {
            expect(firebaseMessagingService.getCurrentToken()).toBeNull();
        });

        it("should return current token after successful registration", async () => {
            await firebaseMessagingService.initialize();
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.getToken.mockResolvedValue("test-token");
            mockORPCClient.notification.saveFCMToken.mockResolvedValue({ data: "success" });

            await firebaseMessagingService.requestPermissionAndGetToken();

            expect(firebaseMessagingService.getCurrentToken()).toBe("test-token");
        });
    });

    describe("isServiceInitialized", () => {
        it("should return false initially", () => {
            expect(firebaseMessagingService.isServiceInitialized()).toBe(false);
        });

        it("should return true after initialization", async () => {
            await firebaseMessagingService.initialize();
            expect(firebaseMessagingService.isServiceInitialized()).toBe(true);
        });
    });

    describe("Error Scenarios", () => {
        it("should handle network errors during token registration", async () => {
            await firebaseMessagingService.initialize();
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.getToken.mockResolvedValue("test-token");
            mockORPCClient.notification.saveFCMToken.mockRejectedValue(
                ErrorSimulator.createNetworkError("Network unavailable")
            );

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.error).toBe("Network unavailable");
        });

        it("should handle Firebase errors during token generation", async () => {
            await firebaseMessagingService.initialize();
            NotificationMockService.setPermission("granted");
            const { mockMessaging } = FirebaseMockService.setupFirebaseMocks();
            mockMessaging.getToken.mockRejectedValue(
                ErrorSimulator.createFirebaseError("messaging/invalid-vapid-key", "Invalid VAPID key")
            );

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.error).toBe("Invalid VAPID key");
        });

        it("should handle permission errors", async () => {
            await firebaseMessagingService.initialize();
            const mockNotification = NotificationMockService.setupNotificationMocks("default");
            mockNotification.requestPermission.mockRejectedValue(
                ErrorSimulator.createPermissionError("Permission request failed")
            );

            const result = await firebaseMessagingService.requestPermissionAndGetToken();

            expect(result.error).toBe("Permission request failed");
        });
    });
});
