/**
 * Firebase configuration and initialization
 */
import { initializeApp, FirebaseApp } from "firebase/app";
import { getMessaging, Messaging } from "firebase/messaging";

// Firebase configuration interface
interface FirebaseConfig {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
}

// Environment-based Firebase configuration
const firebaseConfig: FirebaseConfig = {
    apiKey: import.meta.env.VITE_FIREBASE_API_KEY || "AIzaSyCQ-QeWMrBZw8v9idybo_cFwAIlDO6j-z0",
    authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN || "chikara-academy.firebaseapp.com",
    projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID || "chikara-academy",
    storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET || "chikara-academy.appspot.com",
    messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID || "75175802639",
    appId: import.meta.env.VITE_FIREBASE_APP_ID || "1:75175802639:web:a1cc5e6073f185ec46707a",
};

// VAPID key for push notifications
export const VAPID_KEY =
    import.meta.env.VITE_FIREBASE_VAPID_KEY ||
    "BCv5d6-bgzNJ3EmseSvXJNcGKMFwmFMI_bk-CALpT67DSxgY6qT7_mh68egbVsZ2xR9O4OuuzstFz60uzLFFVCk";

// Initialize Firebase app
let firebaseApp: FirebaseApp | null = null;
let messaging: Messaging | null = null;

export const initializeFirebase = (): FirebaseApp => {
    if (!firebaseApp) {
        firebaseApp = initializeApp(firebaseConfig);
    }
    return firebaseApp;
};

export const getFirebaseMessaging = (): Messaging => {
    if (!messaging) {
        const app = initializeFirebase();
        messaging = getMessaging(app);
    }
    return messaging;
};

export { firebaseConfig };
