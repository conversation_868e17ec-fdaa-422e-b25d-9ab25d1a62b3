/**
 * Firebase services main export
 */
export { firebaseMessagingService } from "./messaging";
export { initializeFirebase, getFirebaseMessaging, VAPID_KEY, firebaseConfig } from "./config";
export * from "./types";

// Legacy exports for backward compatibility
export { firebaseMessagingService as messaging } from "./messaging";

// Convenience functions
export const requestNotificationPermission = async (registration?: ServiceWorkerRegistration) => {
    return await firebaseMessagingService.requestPermissionAndGetToken(registration);
};

export const removeNotificationToken = async (token?: string) => {
    return await firebaseMessagingService.removeTokenFromServer(token);
};
