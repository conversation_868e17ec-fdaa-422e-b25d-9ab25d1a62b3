/**
 * Firebase Cloud Messaging service
 */
import { getToken, onMessage, MessagePayload } from "firebase/messaging";
import { client } from "@/lib/orpc";
import { getFirebaseMessaging, VAPID_KEY } from "./config";
import {
    NotificationPermissionResult,
    TokenRegistrationResult,
    NotificationPermissionStatus,
    FirebaseNotificationPayload,
} from "./types";

class FirebaseMessagingService {
    private messaging = getFirebaseMessaging();
    private currentToken: string | null = null;
    private isInitialized = false;

    /**
     * Initialize the messaging service
     */
    async initialize(): Promise<void> {
        if (this.isInitialized) return;

        try {
            // Set up foreground message listener
            this.setupForegroundMessageListener();
            this.isInitialized = true;
            console.log("Firebase Messaging Service initialized");
        } catch (error) {
            console.error("Failed to initialize Firebase Messaging Service:", error);
            throw error;
        }
    }

    /**
     * Request notification permission and get FCM token
     */
    async requestPermissionAndGetToken(
        serviceWorkerRegistration?: ServiceWorkerRegistration
    ): Promise<NotificationPermissionResult> {
        try {
            // Check if notifications are supported
            if (!("Notification" in window)) {
                return {
                    permission: "denied",
                    error: "Notifications not supported in this browser",
                };
            }

            // Request permission
            const permission = await Notification.requestPermission();

            if (permission !== NotificationPermissionStatus.GRANTED) {
                return {
                    permission,
                    error: permission === "denied" ? "Notifications denied by user" : "Permission not granted",
                };
            }

            // Get FCM token
            const token = await this.getToken(serviceWorkerRegistration);

            if (!token) {
                return {
                    permission,
                    error: "Failed to generate FCM token",
                };
            }

            // Register token with server
            const registrationResult = await this.registerTokenWithServer(token);

            if (!registrationResult.success) {
                return {
                    permission,
                    token,
                    error: registrationResult.error,
                };
            }

            this.currentToken = token;

            return {
                permission,
                token,
            };
        } catch (error) {
            console.error("Error requesting notification permission:", error);
            return {
                permission: "denied",
                error: error instanceof Error ? error.message : "Unknown error",
            };
        }
    }

    /**
     * Get FCM token
     */
    private async getToken(serviceWorkerRegistration?: ServiceWorkerRegistration): Promise<string | null> {
        try {
            const token = await getToken(this.messaging, {
                vapidKey: VAPID_KEY,
                serviceWorkerRegistration,
            });

            return token || null;
        } catch (error) {
            console.error("Error getting FCM token:", error);
            return null;
        }
    }

    /**
     * Register token with server
     */
    private async registerTokenWithServer(token: string): Promise<TokenRegistrationResult> {
        try {
            const response = await client.notification.saveFCMToken({ token });

            return {
                success: true,
                token,
            };
        } catch (error) {
            console.error("Error registering token with server:", error);
            return {
                success: false,
                error: error instanceof Error ? error.message : "Failed to register token",
            };
        }
    }

    /**
     * Remove token from server
     */
    async removeTokenFromServer(token?: string): Promise<boolean> {
        try {
            const tokenToRemove = token || this.currentToken;

            if (!tokenToRemove) {
                console.warn("No token to remove");
                return false;
            }

            await client.notification.removeFCMToken({ token: tokenToRemove });

            if (tokenToRemove === this.currentToken) {
                this.currentToken = null;
            }

            return true;
        } catch (error) {
            console.error("Error removing token from server:", error);
            return false;
        }
    }

    /**
     * Set up foreground message listener
     */
    private setupForegroundMessageListener(): void {
        onMessage(this.messaging, (payload: MessagePayload) => {
            console.log("Received foreground message:", payload);

            // Handle the message (show notification, update UI, etc.)
            this.handleForegroundMessage(payload);
        });
    }

    /**
     * Handle foreground messages
     */
    private handleForegroundMessage(payload: MessagePayload): void {
        const { notification, data } = payload;

        if (notification) {
            // Show browser notification if permission is granted
            if (Notification.permission === "granted") {
                new Notification(notification.title || "Chikara Academy", {
                    body: notification.body,
                    icon: notification.image || "/logo192.png",
                    data: data,
                });
            }
        }

        // Emit custom event for app to handle
        window.dispatchEvent(
            new CustomEvent("firebase-message", {
                detail: payload,
            })
        );
    }

    /**
     * Get current token
     */
    getCurrentToken(): string | null {
        return this.currentToken;
    }

    /**
     * Check if service is initialized
     */
    isServiceInitialized(): boolean {
        return this.isInitialized;
    }
}

// Export singleton instance
export const firebaseMessagingService = new FirebaseMessagingService();
