/**
 * Firebase notification types and interfaces
 */

export interface NotificationPermissionResult {
    permission: NotificationPermission;
    token?: string;
    error?: string;
}

export interface FirebaseNotificationPayload {
    notification?: {
        title?: string;
        body?: string;
        image?: string;
    };
    data?: Record<string, string>;
}

export interface TokenRegistrationResult {
    success: boolean;
    token?: string;
    error?: string;
}

export enum NotificationPermissionStatus {
    GRANTED = "granted",
    DENIED = "denied",
    DEFAULT = "default",
}

export interface NotificationSettings {
    enabled: boolean;
    token?: string;
    lastUpdated?: Date;
}
