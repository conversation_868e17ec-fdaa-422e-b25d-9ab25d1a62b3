// import equipmentIcon from "@/assets/icons/navitems/equipment.png";
// import purpleBG from "@/assets/images/UI/BackgroundImages/purpleBars.jpg";
import { GridPattern } from "@/components/GridPattern";
import { AnimatedGridPattern } from "@/components/ui/animated-grid-pattern";
import { DotPattern } from "@/components/ui/dot-pattern";
import { Ripple } from "@/components/ui/ripple";
import { RetroGrid } from "@/components/ui/retro-grid";
import { FlickeringGrid } from "@/components/ui/flickering-grid";
import trainingImg from "@/assets/images/UI/Skills/strength.png";
import inventoryIcon from "@/assets/icons/navitems/inventory.png";
import characterIcon from "@/assets/icons/navitems/character.png";
import abilitiesIcon from "@/assets/icons/navitems/abilities.png";
import talentsIcon from "@/assets/icons/navitems/talents.png";
import dailyTasksIcon from "@/assets/icons/navitems/tasksOld.png";
import blue2BG from "@/assets/images/UI/BackgroundImages/blue3.jpg";
import blueBG from "@/assets/images/UI/BackgroundImages/blueBars.jpg";
import pinkBG from "@/assets/images/UI/BackgroundImages/pink2.jpg";
import greenBG from "@/assets/images/UI/BackgroundImages/green1.jpg";
import orangeBG from "@/assets/images/UI/BackgroundImages/orangeBars.jpg";
import purple2BG from "@/assets/images/UI/BackgroundImages/purple2.jpg";
import { DisplayAvatar } from "@/components/DisplayAvatar";
import Accomodation from "@/features/home/<USER>/Accomodation";
import Updates from "@/features/home/<USER>/Updates";
import { checkLevelGate } from "@/helpers/levelGates";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { cn } from "@/lib/utils";
import { ChevronRight, GraduationCap, Inbox, Mail, Star } from "lucide-react";
import { Link } from "react-router-dom";
import Spinner from "@/components/Spinners/Spinner";
import useGetUnreadNotifications from "@/hooks/api/useGetUnreadNotifications";
import useGetUnreadMessages from "@/hooks/api/useGetUnreadMessages";

function Home() {
    const { data: currentUser, isLoading, isError } = useFetchCurrentUser();
    const { data: unreadNotifications } = useGetUnreadNotifications();
    const { data: unreadMessages } = useGetUnreadMessages();

    const userLevel = currentUser?.level ?? 0;
    const talentsGate = checkLevelGate("talents", userLevel);
    const dailyQuestsGate = checkLevelGate("dailyQuests", userLevel);

    const newsposts = [
        {
            id: 1,
            name: "Welcome to the Chikara Academy Alpha test!",
            href: "/updates",
            username: "The Headmaster",
            userID: 1,
            date: "May 21, 2023",
            datetime: "2023-05-21",
        },
        {
            id: 2,
            name: "Welcome to Alpha 2!",
            href: "/updates",
            username: "The Headmaster",
            userID: 1,
            date: "Feb 01, 2024",
            datetime: "2024-02-01",
        },
    ];

    const mainFeatures = [
        {
            title: "Character",
            subtitle: "",
            icon: characterIcon,
            background: purple2BG,
            link: "/character",
            locked: false,
        },
        {
            title: "Inventory",
            subtitle: "Manage your items",
            icon: inventoryIcon,
            background: blueBG,
            link: "/inventory",
            locked: false,
        },
        // {
        //     title: "Equipment",
        //     subtitle: "Gear up for battle",
        //     icon: equipmentIcon,
        //     background: purple2BG,
        //     link: "/equipment",
        //     locked: false,
        // },
        {
            title: "Training",
            subtitle: "Train your stats",
            icon: trainingImg,
            background: orangeBG,
            link: "/training",
            locked: false,
        },
        // {
        //     title: "Story Mode",
        //     subtitle: "Follow your journey",
        //     icon: storyIcon,
        //     background: purpleBG,
        //     link: "/story",
        //     locked: false,
        // },
        {
            title: "Talents",
            subtitle: "Unlock new abilities",
            icon: talentsIcon,
            background: greenBG,
            link: "/talents",
            locked: talentsGate.isLocked,
            lockedLevel: talentsGate.requiredLevel,
            points: currentUser?.talentPoints,
        },
        {
            title: "Abilities",
            subtitle: "Master your craft",
            icon: abilitiesIcon,
            background: pinkBG,
            link: "/abilities",
            locked: talentsGate.isLocked,
            lockedLevel: talentsGate.requiredLevel,
        },
        {
            title: "Daily Tasks",
            subtitle: "Complete challenges",
            icon: dailyTasksIcon,
            background: blue2BG,
            link: "/dailies",
            locked: dailyQuestsGate.isLocked,
            lockedLevel: dailyQuestsGate.requiredLevel,
        },
    ];

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-screen">
                <Spinner />
            </div>
        );
    }
    if (isError || !currentUser) {
        return (
            <div className="flex justify-center items-center h-screen">
                <p className="text-red-500">Error loading user data</p>
            </div>
        );
    }

    return (
        <div className="-mx-4 md:-mt-4 flex-1 px-4 md:px-0 relative">
            {/* Enhanced Background Patterns */}
            <div className="absolute inset-0 overflow-hidden pointer-events-none">
                {/* Base Grid Pattern */}
                <GridPattern
                    width={60}
                    height={60}
                    x={-1}
                    y={-1}
                    strokeDasharray="0"
                    className="fill-purple-500/10 stroke-purple-500/10 opacity-30 dark:fill-purple-400/20 dark:stroke-purple-400/20"
                />

                {/* Animated Grid Pattern for dynamic effect */}
                <AnimatedGridPattern
                    numSquares={30}
                    maxOpacity={0.1}
                    duration={3}
                    repeatDelay={1}
                    className={cn(
                        "[mask-image:radial-gradient(500px_circle_at_center,white,transparent)]",
                        "inset-x-0 inset-y-[-30%] h-[200%] skew-y-12 fill-purple-400/30 stroke-purple-400/30"
                    )}
                />

                {/* Dot Pattern for subtle texture */}
                <DotPattern
                    width={20}
                    height={20}
                    cx={1}
                    cy={1}
                    cr={1}
                    className={cn(
                        "[mask-image:radial-gradient(400px_circle_at_center,white,transparent)]",
                        "text-purple-400/20"
                    )}
                />

                {/* Retro Grid for gaming aesthetic */}
                <RetroGrid
                    angle={65}
                    cellSize={80}
                    opacity={0.1}
                    lightLineColor="rgb(147, 51, 234)"
                    darkLineColor="rgb(147, 51, 234)"
                    className="[mask-image:radial-gradient(600px_circle_at_center,white,transparent)]"
                />

                {/* Flickering Grid for dynamic energy */}
                <FlickeringGrid
                    className="absolute inset-0 z-0 size-full [mask-image:radial-gradient(400px_circle_at_center,white,transparent)]"
                    squareSize={4}
                    gridGap={6}
                    color="#9333ea"
                    maxOpacity={0.3}
                    flickerChance={0.1}
                />

                {/* Ripple effect for central focus */}
                <Ripple
                    mainCircleSize={150}
                    mainCircleOpacity={0.1}
                    numCircles={6}
                    className="[mask-image:radial-gradient(300px_circle_at_center,white,transparent)]"
                />

                {/* Gradient overlays for depth */}
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 via-transparent to-blue-500/5" />
                <div className="absolute inset-0 bg-gradient-to-t from-purple-900/10 via-transparent to-transparent" />
            </div>

            {/* Page header */}
            <div className="bg-white shadow-sm dark:bg-slate-800 relative z-10">
                <div className="px-4 sm:px-6 lg:mx-auto lg:max-w-6xl lg:px-8">
                    <div className="flex flex-col py-3 md:flex-row md:items-center md:justify-between lg:border-gray-200 lg:border-t 2xl:py-6 dark:border-none">
                        <div className="min-w-0 flex-1">
                            {/* Profile */}
                            <div className="flex items-center">
                                <DisplayAvatar
                                    className="h-14 w-auto rounded-full sm:block 2xl:size-16 dark:border dark:border-slate-500"
                                    src={currentUser}
                                />
                                <div>
                                    <div className="mt-1 flex items-center">
                                        <h1 className="ml-3 font-display font-bold text-gray-800 text-xl leading-7 sm:truncate sm:leading-9 2xl:text-2xl dark:text-gray-50">
                                            Welcome Home
                                            {currentUser?.username && `, ${currentUser?.username?.split(" ")[0]}`}
                                        </h1>
                                    </div>

                                    <dl className="ml-2 flex flex-row gap-4 md:ml-3 md:flex-wrap md:gap-0 2xl:mt-2">
                                        <dt className="sr-only">New Events</dt>
                                        <dd className="flex items-center font-bold text-gray-500 text-sm capitalize sm:mr-6 dark:text-gray-400 ">
                                            <Inbox className="mr-1.5 size-5 shrink-0 " />
                                            {unreadNotifications?.unread ?? 0} New Events
                                        </dd>
                                        <dt className="sr-only">New Messages</dt>
                                        <dd className="flex items-center font-bold text-gray-500 text-sm capitalize sm:mr-6 dark:text-gray-400">
                                            <Mail className="mr-1.5 size-5 shrink-0 " />
                                            {unreadMessages?.unread ?? 0} New Messages
                                        </dd>
                                    </dl>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div className="mt-6">
                <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 space-y-6">
                    <div className="grid grid-cols-2 lg:grid-cols-3 gap-6">
                        {mainFeatures.map((feature, index) => (
                            <Link
                                key={index}
                                to={feature.locked ? "#" : feature.link}
                                className={cn(
                                    "group relative overflow-hidden rounded-2xl transition-all duration-300",
                                    feature.locked ? "cursor-not-allowed" : "hover:scale-105 hover:shadow-2xl"
                                )}
                            >
                                <div
                                    className="h-48 bg-cover bg-center relative"
                                    style={{ backgroundImage: `url(${feature.background})` }}
                                >
                                    <div
                                        className={cn(
                                            "absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent",
                                            feature.locked && "grayscale"
                                        )}
                                    >
                                        <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 to-pink-900/20"></div>
                                    </div>

                                    {/* Feature Icon */}
                                    <div className="absolute top-4 left-4">
                                        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-3">
                                            <img
                                                src={feature.icon}
                                                alt={feature.title}
                                                className={cn(
                                                    "h-8 w-8 transition-transform group-hover:scale-110",
                                                    feature.locked && "grayscale"
                                                )}
                                            />
                                        </div>
                                    </div>

                                    {/* Talent Points Badge */}
                                    {feature.points && !feature.locked && (
                                        <div className="absolute top-4 right-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full px-3 py-1 flex items-center gap-1">
                                            <Star className="h-4 w-4 text-white fill-white" />
                                            <span className="text-white font-bold text-sm">{feature.points}</span>
                                        </div>
                                    )}

                                    {/* Lock Badge */}
                                    {feature.locked && (
                                        <div className="absolute top-4 right-4 bg-red-500/90 rounded-full px-3 py-1">
                                            <span className="text-white font-bold text-sm">
                                                Lv.{feature.lockedLevel}
                                            </span>
                                        </div>
                                    )}

                                    {/* Content */}
                                    <div className="absolute bottom-0 left-0 right-0 p-4">
                                        <h3 className="text-white font-bold text-xl mb-1">{feature.title}</h3>
                                        <p className="text-white/80 text-sm">{feature.subtitle}</p>
                                        {feature.locked && (
                                            <p className="text-red-400 text-xs mt-1">
                                                Requires Level {feature.lockedLevel}
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </Link>
                        ))}
                    </div>
                    <div className="grid md:grid-cols-2 gap-6">
                        <Accomodation />
                        <Updates />
                    </div>
                </div>

                {/* Activity list (smallest breakpoint only) */}
                <h2 className="mx-auto mt-5 max-w-6xl px-4 text-left font-medium text-slate-700 text-sm uppercase leading-6 tracking-wider sm:hidden sm:px-6 lg:px-10 dark:text-slate-200 ">
                    LATEST NEWS
                </h2>
                <div className="text-shadow shadow-sm sm:hidden">
                    <ul className="mt-1 divide-y divide-gray-200 overflow-hidden shadow-sm sm:hidden dark:divide-gray-600">
                        {newsposts.reverse().map((newspost) => (
                            <li key={newspost.id}>
                                <Link
                                    to={newspost.href}
                                    className="block bg-white p-4 hover:bg-gray-50 dark:bg-gray-800"
                                >
                                    <span className="flex items-center space-x-4">
                                        <span className="flex flex-1 space-x-2 truncate">
                                            <GraduationCap
                                                className="size-5 shrink-0 text-gray-300"
                                                aria-hidden="true"
                                            />
                                            <span className="flex flex-col truncate text-slate-800 text-xs dark:text-gray-300">
                                                <span className="truncate">{newspost.name}</span>
                                                <span className="flex flex-row">
                                                    <time dateTime={newspost.datetime}>{newspost.date}</time>
                                                </span>
                                            </span>
                                        </span>
                                        <ChevronRight className="size-5 shrink-0 text-gray-400" aria-hidden="true" />
                                    </span>
                                </Link>
                            </li>
                        ))}
                    </ul>

                    <nav
                        className="flex items-center justify-between border-gray-200 border-t bg-white px-4 py-3 dark:border-gray-600 dark:bg-gray-700"
                        aria-label="Pagination"
                    >
                        <div className="flex flex-1 justify-between">
                            <button
                                disabled
                                className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-400 text-sm hover:text-gray-400 dark:border-gray-600 dark:bg-gray-800"
                            >
                                Previous
                            </button>
                            <button
                                disabled
                                className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-400 text-sm hover:text-gray-400 dark:border-gray-600 dark:bg-gray-800"
                            >
                                Next
                            </button>
                        </div>
                    </nav>
                </div>

                {/* Activity table (small breakpoint and up) */}
                <div className="hidden sm:block ">
                    <div className="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8 ">
                        <div className="mt-6 flex flex-col">
                            <div className="min-w-full overflow-hidden overflow-x-auto align-middle shadow-sm sm:rounded-lg ">
                                <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                                    <thead>
                                        <tr>
                                            <th className="bg-gray-50 px-6 py-3 text-left font-medium text-gray-500 text-xs uppercase tracking-wider dark:bg-gray-800 dark:text-gray-400">
                                                LATEST NEWS
                                            </th>
                                            <th className="hidden bg-gray-50 px-8 py-3 text-center font-medium text-gray-500 text-xs uppercase tracking-wider md:block dark:bg-gray-800 dark:text-gray-400">
                                                Created By
                                            </th>
                                            <th className="bg-gray-50 px-12 py-3 text-right font-medium text-gray-500 text-xs uppercase tracking-wider dark:bg-gray-800 dark:text-gray-400">
                                                Date
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
                                        {newsposts.map((newspost) => (
                                            <tr key={newspost.id} className="bg-white dark:bg-gray-800">
                                                <td className="w-full max-w-0 whitespace-nowrap px-6 py-4 text-gray-900 text-sm ">
                                                    <div className="flex">
                                                        <Link
                                                            to={newspost.href}
                                                            className="group inline-flex space-x-2 truncate text-sm"
                                                        >
                                                            <GraduationCap
                                                                className="size-5 shrink-0 text-gray-500 group-hover:text-gray-500 dark:text-gray-400"
                                                                aria-hidden="true"
                                                            />
                                                            <p className="truncate text-slate-700 group-hover:text-slate-900 dark:text-gray-300 dark:group-hover:text-gray-400">
                                                                {newspost.name}
                                                            </p>
                                                        </Link>
                                                    </div>
                                                </td>

                                                <td className="hidden whitespace-nowrap px-6 py-4 text-gray-600 text-sm md:block">
                                                    <Link to={`/profile/${newspost.userID}`}>
                                                        <span
                                                            className={cn(
                                                                "inline-flex items-center rounded-full px-2.5 py-0.5 font-medium text-xs capitalize dark:text-gray-300"
                                                            )}
                                                        >
                                                            {newspost.username}
                                                        </span>
                                                    </Link>
                                                </td>
                                                <td className="whitespace-nowrap px-6 py-4 text-right text-gray-600 text-sm dark:text-gray-300">
                                                    <time dateTime={newspost.datetime}>{newspost.date}</time>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                                {/* Pagination */}
                                <nav
                                    className="flex items-center justify-between border-gray-200 border-t bg-white px-4 py-2 sm:px-6 dark:border-gray-600 dark:bg-gray-800"
                                    aria-label="Pagination"
                                >
                                    <div className="hidden sm:block">
                                        <p className="text-gray-700 text-sm dark:text-gray-300">
                                            Showing <span className="font-medium">1</span> to{" "}
                                            <span className="font-medium">1</span> of{" "}
                                            <span className="font-medium">1</span> results
                                        </p>
                                    </div>
                                    <div className="flex flex-1 justify-between sm:justify-end">
                                        <button
                                            disabled
                                            className="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-400 text-sm dark:border-gray-700 dark:bg-gray-800"
                                        >
                                            Previous
                                        </button>
                                        <button
                                            disabled
                                            className="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 font-medium text-gray-400 text-sm dark:border-gray-700 dark:bg-gray-800"
                                        >
                                            Next
                                        </button>
                                    </div>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Home;
