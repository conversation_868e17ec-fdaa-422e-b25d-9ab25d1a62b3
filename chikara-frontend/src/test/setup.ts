/**
 * Test setup and configuration for frontend tests
 */
import { beforeAll, afterAll, beforeEach, afterEach, vi } from "vitest";
import { cleanup } from "@testing-library/react";
import "@testing-library/jest-dom";

// Mock Firebase
const mockFirebaseApp = {
    name: "test-app",
    options: {
        apiKey: "test-api-key",
        projectId: "test-project",
    },
};

const mockMessaging = {
    getToken: vi.fn(),
    onMessage: vi.fn(),
    deleteToken: vi.fn(),
};

// Mock Firebase modules
vi.mock("firebase/app", () => ({
    initializeApp: vi.fn(() => mockFirebaseApp),
    getApps: vi.fn(() => []),
    getApp: vi.fn(() => mockFirebaseApp),
}));

vi.mock("firebase/messaging", () => ({
    getMessaging: vi.fn(() => mockMessaging),
    getToken: vi.fn(),
    onMessage: vi.fn(),
    deleteToken: vi.fn(),
}));

// Mock service worker
const mockServiceWorkerRegistration = {
    active: {
        postMessage: vi.fn(),
    },
    installing: null,
    waiting: null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
    scope: "http://localhost:3000/",
    unregister: vi.fn(),
    update: vi.fn(),
};

Object.defineProperty(window, "navigator", {
    value: {
        serviceWorker: {
            register: vi.fn(() => Promise.resolve(mockServiceWorkerRegistration)),
            ready: Promise.resolve(mockServiceWorkerRegistration),
            controller: null,
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
        },
    },
    writable: true,
});

// Mock Notification API
const mockNotification = vi.fn();
mockNotification.permission = "default";
mockNotification.requestPermission = vi.fn(() => Promise.resolve("granted"));

Object.defineProperty(window, "Notification", {
    value: mockNotification,
    writable: true,
});

// Mock environment variables
vi.mock("import.meta", () => ({
    env: {
        VITE_FIREBASE_API_KEY: "test-api-key",
        VITE_FIREBASE_AUTH_DOMAIN: "test-project.firebaseapp.com",
        VITE_FIREBASE_PROJECT_ID: "test-project",
        VITE_FIREBASE_STORAGE_BUCKET: "test-project.appspot.com",
        VITE_FIREBASE_MESSAGING_SENDER_ID: "123456789",
        VITE_FIREBASE_APP_ID: "1:123456789:web:abcdef",
        VITE_FIREBASE_VAPID_KEY: "test-vapid-key",
    },
}));

// Mock ORPC client
vi.mock("@/lib/orpc", () => ({
    client: {
        notification: {
            saveFCMToken: vi.fn(() => Promise.resolve({ data: "Token saved" })),
            removeFCMToken: vi.fn(() => Promise.resolve({ data: "Token removed" })),
            updatePushNotificationSettings: vi.fn(() => Promise.resolve({ data: {} })),
            getPushNotificationSettings: vi.fn(() =>
                Promise.resolve({
                    data: { pushNotificationsEnabled: true, tokenCount: 1, hasTokens: true },
                })
            ),
        },
    },
}));

// Setup and cleanup
beforeAll(() => {
    // Global test setup
    console.log("Frontend test environment initialized");
});

afterAll(() => {
    // Global test cleanup
    console.log("Frontend test environment cleaned up");
});

beforeEach(() => {
    // Reset mocks before each test
    vi.clearAllMocks();

    // Reset Notification permission
    mockNotification.permission = "default";
});

afterEach(() => {
    // Cleanup after each test
    cleanup();
    vi.clearAllTimers();
});

// Test utilities
export const createMockServiceWorkerRegistration = (overrides = {}) => ({
    ...mockServiceWorkerRegistration,
    ...overrides,
});

export const createMockNotificationPayload = (overrides = {}) => ({
    notification: {
        title: "Test Notification",
        body: "This is a test notification",
        image: "/test-icon.png",
    },
    data: {
        type: "test",
        userId: "1",
    },
    ...overrides,
});

export const setNotificationPermission = (permission: NotificationPermission) => {
    mockNotification.permission = permission;
    mockNotification.requestPermission.mockResolvedValue(permission);
};

export const mockFirebaseToken = "mock-firebase-token-" + Math.random().toString(36).substring(7);

// Export mocks for use in tests
export { mockFirebaseApp, mockMessaging, mockServiceWorkerRegistration, mockNotification };
