/**
 * Test utilities and helpers for frontend testing
 */
import React, { ReactElement } from "react";
import { render, RenderOptions } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { vi } from "vitest";

// Test data factories
export class FrontendTestDataFactory {
    static createMockFirebaseConfig() {
        return {
            apiKey: "test-api-key",
            authDomain: "test-project.firebaseapp.com",
            projectId: "test-project",
            storageBucket: "test-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "1:123456789:web:abcdef",
        };
    }

    static createMockNotificationPayload(overrides: Partial<any> = {}) {
        return {
            notification: {
                title: "Test Notification",
                body: "This is a test notification",
                image: "/test-icon.png",
            },
            data: {
                type: "levelup",
                userId: "1",
                details: JSON.stringify({ level: 5 }),
            },
            ...overrides,
        };
    }

    static createMockPermissionResult(overrides: Partial<any> = {}) {
        return {
            permission: "granted" as NotificationPermission,
            token: "mock-fcm-token",
            error: undefined,
            ...overrides,
        };
    }

    static createMockServiceWorkerRegistration(overrides: Partial<any> = {}) {
        return {
            active: {
                postMessage: vi.fn(),
            },
            installing: null,
            waiting: null,
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn(),
            scope: "http://localhost:3000/",
            unregister: vi.fn().mockResolvedValue(true),
            update: vi.fn().mockResolvedValue(undefined),
            ...overrides,
        };
    }
}

// Mock Firebase services
export class FirebaseMockService {
    static mockMessaging = {
        getToken: vi.fn(),
        onMessage: vi.fn(),
        deleteToken: vi.fn(),
    };

    static mockFirebaseApp = {
        name: "test-app",
        options: FrontendTestDataFactory.createMockFirebaseConfig(),
    };

    static setupFirebaseMocks() {
        // Reset all mocks
        vi.clearAllMocks();

        // Setup default mock implementations
        this.mockMessaging.getToken.mockResolvedValue("mock-fcm-token");
        this.mockMessaging.onMessage.mockImplementation((callback) => {
            // Store callback for later use in tests
            (window as any).__firebaseMessageCallback = callback;
        });
        this.mockMessaging.deleteToken.mockResolvedValue(true);

        return {
            messaging: this.mockMessaging,
            app: this.mockFirebaseApp,
        };
    }

    static triggerForegroundMessage(payload: any) {
        const callback = (window as any).__firebaseMessageCallback;
        if (callback) {
            callback(payload);
        }
    }
}

// Notification API mocks
export class NotificationMockService {
    static setupNotificationMocks(permission: NotificationPermission = "default") {
        const mockNotification = vi.fn();
        mockNotification.permission = permission;
        mockNotification.requestPermission = vi.fn().mockResolvedValue(permission);

        Object.defineProperty(window, "Notification", {
            value: mockNotification,
            writable: true,
        });

        return mockNotification;
    }

    static setPermission(permission: NotificationPermission) {
        if (window.Notification) {
            (window.Notification as any).permission = permission;
            (window.Notification.requestPermission as any).mockResolvedValue(permission);
        }
    }
}

// Service Worker mocks
export class ServiceWorkerMockService {
    static setupServiceWorkerMocks() {
        const mockRegistration = FrontendTestDataFactory.createMockServiceWorkerRegistration();

        Object.defineProperty(window.navigator, "serviceWorker", {
            value: {
                register: vi.fn().mockResolvedValue(mockRegistration),
                ready: Promise.resolve(mockRegistration),
                controller: null,
                addEventListener: vi.fn(),
                removeEventListener: vi.fn(),
            },
            writable: true,
        });

        return mockRegistration;
    }
}

// React Testing Library wrapper with providers
interface CustomRenderOptions extends Omit<RenderOptions, "wrapper"> {
    queryClient?: QueryClient;
}

export function renderWithProviders(ui: ReactElement, options: CustomRenderOptions = {}) {
    const {
        queryClient = new QueryClient({
            defaultOptions: {
                queries: {
                    retry: false,
                },
            },
        }),
        ...renderOptions
    } = options;

    function Wrapper({ children }: { children: React.ReactNode }) {
        return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>;
    }

    return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Custom hooks testing utilities
export function createMockHookReturn(overrides: Partial<any> = {}) {
    return {
        isInitialized: true,
        permission: "default" as NotificationPermission,
        token: null,
        isLoading: false,
        error: null,
        requestPermission: vi.fn(),
        removeToken: vi.fn(),
        ...overrides,
    };
}

// Event simulation utilities
export class EventSimulator {
    static simulateServiceWorkerMessage(data: any) {
        const event = new MessageEvent("message", { data });
        window.dispatchEvent(event);
    }

    static simulateNotificationClick(notification: any) {
        const event = new Event("notificationclick");
        (event as any).notification = notification;
        window.dispatchEvent(event);
    }

    static simulateFirebaseMessage(payload: any) {
        const event = new CustomEvent("firebase-message", { detail: payload });
        window.dispatchEvent(event);
    }

    static simulatePermissionChange(permission: NotificationPermission) {
        NotificationMockService.setPermission(permission);
        const event = new Event("permissionchange");
        window.dispatchEvent(event);
    }
}

// Async utilities
export class AsyncTestUtils {
    static async waitForNextTick() {
        return new Promise((resolve) => setTimeout(resolve, 0));
    }

    static async waitForCondition(
        condition: () => boolean,
        timeout: number = 5000,
        interval: number = 100
    ): Promise<void> {
        const start = Date.now();

        while (!condition() && Date.now() - start < timeout) {
            await new Promise((resolve) => setTimeout(resolve, interval));
        }

        if (!condition()) {
            throw new Error(`Condition not met within ${timeout}ms`);
        }
    }

    static createDelayedPromise<T>(value: T, delay: number = 100): Promise<T> {
        return new Promise((resolve) => {
            setTimeout(() => resolve(value), delay);
        });
    }
}

// Error simulation utilities
export class ErrorSimulator {
    static createNetworkError(message: string = "Network error") {
        const error = new Error(message);
        (error as any).name = "NetworkError";
        return error;
    }

    static createFirebaseError(code: string, message: string) {
        const error = new Error(message);
        (error as any).code = code;
        (error as any).name = "FirebaseError";
        return error;
    }

    static createPermissionError(message: string = "Permission denied") {
        const error = new Error(message);
        (error as any).name = "NotAllowedError";
        return error;
    }
}

// Performance testing utilities
export class FrontendPerformanceUtils {
    static measureRenderTime(renderFn: () => void): number {
        const start = performance.now();
        renderFn();
        const end = performance.now();
        return end - start;
    }

    static async measureAsyncOperation<T>(operation: () => Promise<T>): Promise<{ result: T; duration: number }> {
        const start = performance.now();
        const result = await operation();
        const end = performance.now();
        return {
            result,
            duration: end - start,
        };
    }
}

// Export all utilities
export {
    FrontendTestDataFactory,
    FirebaseMockService,
    NotificationMockService,
    ServiceWorkerMockService,
    EventSimulator,
    AsyncTestUtils,
    ErrorSimulator,
    FrontendPerformanceUtils,
};

// Re-export testing library utilities
export * from "@testing-library/react";
export * from "@testing-library/user-event";
