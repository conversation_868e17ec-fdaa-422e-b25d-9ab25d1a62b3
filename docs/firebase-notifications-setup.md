# Firebase Notifications Setup Guide

This document outlines the improved Firebase Cloud Messaging (FCM) setup for Chikara Academy, including both frontend and backend implementations.

## Overview

The Firebase notifications system has been refactored to provide:

- **Type-safe implementation** with proper TypeScript interfaces
- **Environment-based configuration** for better security
- **Improved error handling** and logging
- **User preference management** for push notifications
- **Modular architecture** with clear separation of concerns
- **React hooks** for easy frontend integration

## Architecture

### Frontend Structure

```
chikara-frontend/src/lib/firebase/
├── config.ts          # Firebase configuration and initialization
├── messaging.ts       # FCM service class
├── types.ts          # TypeScript interfaces
└── index.ts          # Main exports and convenience functions

chikara-frontend/src/hooks/
└── useFirebaseNotifications.ts  # React hook for notifications
```

### Backend Structure

```
chikara-backend/src/config/
└── firebase.ts       # Firebase Admin SDK setup

chikara-backend/src/core/
└── notification.service.ts  # Notification business logic

chikara-backend/src/features/notification/
└── notification.controller.ts  # API endpoints
```

## Setup Instructions

### 1. Environment Variables

#### Frontend (.env)

```bash
VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_VAPID_KEY=your_vapid_key
```

#### Backend (.env)

```bash
FIREBASE_ENABLED=true
FIREBASE_PROJECT_ID=your_project_id
FIREBASE_CLIENT_EMAIL=your_service_account_email
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
```

### 2. Service Worker Setup

The service worker (`firebase-messaging-sw.js`) handles background notifications and includes:

- Background message handling
- Custom notification display
- Click handling with app focus/opening
- Workbox integration for PWA features

### 3. Frontend Integration

#### Using the React Hook

```typescript
import { useFirebaseNotifications } from '@/hooks/useFirebaseNotifications';

function NotificationSettings() {
  const {
    permission,
    token,
    isLoading,
    error,
    requestPermission,
    removeToken
  } = useFirebaseNotifications();

  const handleEnableNotifications = async () => {
    const result = await requestPermission();
    if (result.token) {
      console.log('Notifications enabled!');
    }
  };

  return (
    <div>
      <p>Permission: {permission}</p>
      <p>Token: {token ? 'Active' : 'None'}</p>
      {error && <p>Error: {error}</p>}

      <button
        onClick={handleEnableNotifications}
        disabled={isLoading}
      >
        Enable Notifications
      </button>

      {token && (
        <button onClick={removeToken}>
          Disable Notifications
        </button>
      )}
    </div>
  );
}
```

#### Direct Service Usage

```typescript
import { firebaseMessagingService } from "@/lib/firebase";

// Initialize the service
await firebaseMessagingService.initialize();

// Request permission and get token
const result = await firebaseMessagingService.requestPermissionAndGetToken();

// Remove token
await firebaseMessagingService.removeTokenFromServer();
```

### 4. Backend Integration

#### Sending Notifications

```typescript
import { NotifyUser, sendSinglePushNotification } from "../core/notification.service";
import { NotificationTypes } from "../types/notification";

// Send notification with push (recommended)
await NotifyUser(
    userId,
    NotificationTypes.levelup,
    { level: 25, experience: 1000 },
    false, // read status
    true // send push notification
);

// Send only push notification
await sendSinglePushNotification(userId, {
    title: "Custom Title",
    body: "Custom message",
    data: { customData: "value" },
});
```

#### Managing User Preferences

```typescript
// Update user's push notification preference
await UpdatePushNotificationSettings(userId, true);

// Get user's current settings
const settings = await getPushNotificationSettings(userId);
```

## API Endpoints

### Save FCM Token

```
POST /api/notification/save-fcm-token
Body: { token: string }
```

### Remove FCM Token

```
DELETE /api/notification/remove-fcm-token
Body: { token: string }
```

### Update Push Settings

```
PUT /api/notification/push-settings
Body: { pushEnabled: boolean }
```

### Get Push Settings

```
GET /api/notification/push-settings
```

## Notification Types

The system supports various notification types with custom titles and messages:

- `levelup` - Level progression notifications
- `fight_win` - Battle victory notifications
- `transfer_received` - Money transfer notifications
- `quest_complete` - Quest completion notifications
- `crafting_completion` - Crafting completion notifications
- `gang_invite` - Gang invitation notifications
- `friend_request` - Friend request notifications
- `bounty_placed` - Bounty alert notifications
- `lottery_won` - Lottery win notifications

## Security Considerations

1. **Environment Variables**: All sensitive configuration is stored in environment variables
2. **Token Validation**: FCM tokens are validated before storage
3. **User Preferences**: Push notifications respect user preferences
4. **Error Handling**: Graceful error handling prevents service disruption
5. **Logging**: Comprehensive logging for debugging and monitoring

## Testing

### Frontend Testing

```bash
# Test notification permission request
# Open browser dev tools and check console for logs

# Test service worker
# Check Application tab in dev tools for service worker status
```

### Backend Testing

```bash
# Test Firebase initialization
curl -X POST http://localhost:3000/api/test/firebase-init

# Test push notification
curl -X POST http://localhost:3000/api/test/push-notification \
  -H "Content-Type: application/json" \
  -d '{"userId": 1, "message": "Test notification"}'
```

## Troubleshooting

### Common Issues

1. **Service Worker Not Registering**
    - Check if service worker file is accessible
    - Verify HTTPS is enabled (required for notifications)
    - Check browser console for registration errors

2. **Notifications Not Appearing**
    - Verify user has granted permission
    - Check if user has push notifications enabled in settings
    - Verify FCM token is valid and saved
    - Check Firebase console for delivery status

3. **Token Registration Failing**
    - Verify VAPID key is correct
    - Check network connectivity
    - Verify backend API is accessible

4. **Backend Firebase Errors**
    - Verify Firebase service account credentials
    - Check if Firebase project ID is correct
    - Ensure Firebase Admin SDK is properly initialized

### Debug Logging

Enable debug logging by setting:

```bash
# Backend
LOG_LEVEL=debug

# Frontend (browser console)
localStorage.setItem('firebase-debug', 'true');
```

## Migration from Old System

If migrating from the old Firebase setup:

1. Update environment variables
2. Replace old Firebase imports with new ones
3. Update notification request calls to use new API
4. Test thoroughly in development environment

## Performance Considerations

- Tokens are cached to avoid unnecessary API calls
- Push notifications are batched when sending to multiple users
- Database queries are optimized for user preference checks
- Error handling prevents cascading failures

## Future Enhancements

- Push notification scheduling
- Rich notifications with images and actions
- Notification categories and filtering
- Analytics and delivery tracking
- A/B testing for notification content
