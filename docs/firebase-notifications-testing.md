# Firebase Notifications Testing Guide

This guide provides comprehensive information about testing the Firebase Cloud Messaging (FCM) implementation in Chikara Academy.

## Overview

The testing strategy covers multiple layers:

- **Unit Tests**: Individual functions and components
- **Integration Tests**: End-to-end notification flows
- **Performance Tests**: Load and stress testing
- **Security Tests**: Vulnerability and compliance testing
- **Manual Tests**: Local development and debugging

## Test Structure

### Frontend Tests

```
chikara-frontend/src/
├── lib/firebase/__tests__/
│   ├── config.test.ts
│   ├── messaging.test.ts
│   └── types.test.ts
├── hooks/__tests__/
│   └── useFirebaseNotifications.test.ts
├── components/__tests__/
│   └── NotificationSettings.test.tsx
└── test/
    ├── setup.ts
    └── testUtils.tsx
```

### Backend Tests

```
chikara-backend/src/
├── config/__tests__/
│   └── firebase.test.ts
├── core/__tests__/
│   └── notification.service.test.ts
├── features/notification/__tests__/
│   └── notification.controller.test.ts
├── repositories/__tests__/
│   └── notification.repository.test.ts
└── test/
    ├── setup.ts
    └── testUtils.ts
```

## Running Tests

### Local Testing

#### Quick Test Run

```bash
# Run all tests
node scripts/run-tests.js

# Run only frontend tests
node scripts/run-tests.js --skip-backend

# Run only backend tests
node scripts/run-tests.js --skip-frontend

# Run with verbose output
node scripts/run-tests.js --verbose
```

#### Individual Test Suites

```bash
# Frontend tests
cd chikara-frontend
bun test

# Backend tests
cd chikara-backend
bun test

# With coverage
bun test --coverage

# Watch mode
bun test --watch
```

### Firebase Emulator Testing

#### Start Local Environment

```bash
# Full local setup with emulator
node scripts/test-firebase-local.js

# Skip emulator (use real Firebase)
node scripts/test-firebase-local.js --skip-emulator

# Only run tests
node scripts/test-firebase-local.js --test-only
```

#### Manual Emulator Setup

```bash
# Install Firebase CLI
npm install -g firebase-tools

# Start emulator
firebase emulators:start

# In another terminal, run tests
bun test
```

## Test Categories

### 1. Unit Tests

#### Frontend Unit Tests

**Firebase Configuration Tests** (`config.test.ts`)

- Environment variable loading
- Firebase app initialization
- Messaging service setup
- Error handling

**Messaging Service Tests** (`messaging.test.ts`)

- Token generation and management
- Permission handling
- Message listening
- Server communication
- Error scenarios

**React Hook Tests** (`useFirebaseNotifications.test.ts`)

- State management
- Side effects
- Permission flows
- Token lifecycle
- Error handling

**Component Tests** (`NotificationSettings.test.tsx`)

- Rendering with different states
- User interactions
- Loading states
- Error display
- Accessibility

#### Backend Unit Tests

**Firebase Configuration Tests** (`firebase.test.ts`)

- Admin SDK initialization
- Credential validation
- Push notification sending
- Batch operations
- Error handling

**Notification Service Tests** (`notification.service.test.ts`)

- Notification creation
- Push notification logic
- User preference handling
- Message generation
- Error scenarios

**Controller Tests** (`notification.controller.test.ts`)

- API endpoint functionality
- Input validation
- Authentication
- Error responses
- Business logic

**Repository Tests** (`notification.repository.test.ts`)

- Database operations
- Query optimization
- Error handling
- Data integrity

### 2. Integration Tests

#### End-to-End Notification Flow

```typescript
// Example integration test
describe("Notification Flow Integration", () => {
    it("should complete full notification cycle", async () => {
        // 1. User requests permission
        const permission = await requestNotificationPermission();
        expect(permission.permission).toBe("granted");

        // 2. Token is registered with server
        expect(permission.token).toBeTruthy();

        // 3. Server sends notification
        await triggerNotification(userId, "levelup", { level: 5 });

        // 4. Notification is received
        await waitForNotification();
        expect(lastNotification.title).toBe("Level Up!");
    });
});
```

#### Service Worker Integration

```typescript
describe("Service Worker Integration", () => {
    it("should handle background messages", async () => {
        // Test background message handling
        // Test notification click handling
        // Test service worker updates
    });
});
```

### 3. Performance Tests

#### Load Testing

```typescript
describe("Performance Tests", () => {
    it("should handle 1000 concurrent token registrations", async () => {
        const promises = Array.from({ length: 1000 }, (_, i) => registerToken(`user-${i}`, `token-${i}`));

        const results = await Promise.allSettled(promises);
        const successful = results.filter((r) => r.status === "fulfilled").length;

        expect(successful).toBeGreaterThan(950); // 95% success rate
    });

    it("should send batch notifications efficiently", async () => {
        const start = performance.now();
        await sendBatchNotifications(1000);
        const duration = performance.now() - start;

        expect(duration).toBeLessThan(5000); // Under 5 seconds
    });
});
```

#### Memory Testing

```typescript
describe("Memory Tests", () => {
    it("should not leak memory during token operations", async () => {
        const initialMemory = process.memoryUsage().heapUsed;

        // Perform many operations
        for (let i = 0; i < 1000; i++) {
            await registerAndRemoveToken(`test-token-${i}`);
        }

        // Force garbage collection
        if (global.gc) global.gc();

        const finalMemory = process.memoryUsage().heapUsed;
        const memoryIncrease = finalMemory - initialMemory;

        expect(memoryIncrease).toBeLessThan(10 * 1024 * 1024); // Less than 10MB
    });
});
```

### 4. Security Tests

#### Input Validation

```typescript
describe("Security Tests", () => {
    it("should validate FCM tokens", async () => {
        const invalidTokens = [
            "", // Empty
            "x", // Too short
            "invalid-token", // Invalid format
            '<script>alert("xss")</script>', // XSS attempt
            "DROP TABLE users;", // SQL injection attempt
        ];

        for (const token of invalidTokens) {
            const result = await saveFCMToken(1, token);
            expect(result.error).toBeTruthy();
        }
    });

    it("should prevent unauthorized access", async () => {
        // Test without authentication
        const result = await saveFCMToken(null, "valid-token");
        expect(result.statusCode).toBe(401);
    });
});
```

#### Data Protection

```typescript
describe("Data Protection Tests", () => {
    it("should not expose sensitive data in logs", async () => {
        const logSpy = vi.spyOn(console, "log");

        await sendPushNotification(1, "sensitive-token", { body: "test" });

        const logs = logSpy.mock.calls.flat().join(" ");
        expect(logs).not.toContain("sensitive-token");
    });
});
```

## Test Data Management

### Test Data Factories

```typescript
// Frontend test data
export class FrontendTestDataFactory {
    static createMockNotificationPayload(overrides = {}) {
        return {
            notification: {
                title: "Test Notification",
                body: "Test message",
                image: "/test-icon.png",
            },
            data: { type: "test", userId: "1" },
            ...overrides,
        };
    }
}

// Backend test data
export class TestDataFactory {
    static createUser(overrides = {}) {
        return {
            id: Math.floor(Math.random() * 1000) + 1,
            username: `testuser_${Math.random().toString(36).substring(7)}`,
            pushNotificationsEnabled: true,
            ...overrides,
        };
    }
}
```

### Database Seeding

```typescript
export class DatabaseTestHelper {
    async seedTestData() {
        const user = await this.seedTestUser();
        const tokens = await this.seedTestPushTokens(user.id, 3);
        const notifications = await this.seedTestNotifications(user.id, 5);

        return { user, tokens, notifications };
    }

    async cleanupTestData() {
        await this.db.push_token.deleteMany({ where: { token: { contains: "test_" } } });
        await this.db.notification.deleteMany({ where: { details: { contains: "test" } } });
        await this.db.user.deleteMany({ where: { username: { contains: "testuser_" } } });
    }
}
```

## Mocking Strategies

### Firebase Mocking

```typescript
// Mock Firebase Admin
export const mockFirebaseAdmin = {
    initializeApp: vi.fn(),
    messaging: () => ({
        send: vi.fn().mockResolvedValue("message-id"),
        sendMulticast: vi.fn().mockResolvedValue({ successCount: 1 }),
    }),
};

// Mock Firebase Client
export const mockFirebaseClient = {
    initializeApp: vi.fn(),
    getMessaging: vi.fn(),
    getToken: vi.fn().mockResolvedValue("test-token"),
    onMessage: vi.fn(),
};
```

### Service Worker Mocking

```typescript
export class ServiceWorkerMockService {
    static setupServiceWorkerMocks() {
        Object.defineProperty(window.navigator, "serviceWorker", {
            value: {
                register: vi.fn().mockResolvedValue(mockRegistration),
                ready: Promise.resolve(mockRegistration),
            },
        });
    }
}
```

## Continuous Integration

### GitHub Actions Workflow

The CI pipeline includes:

1. **Frontend Tests**: Unit tests, component tests, type checking
2. **Backend Tests**: Service tests, API tests, database tests
3. **Integration Tests**: End-to-end flows with Firebase emulator
4. **Security Tests**: Vulnerability scanning, secret detection
5. **Quality Gates**: Coverage thresholds, performance benchmarks

### Quality Metrics

- **Code Coverage**: Minimum 80% for critical paths
- **Test Performance**: Tests complete within 10 minutes
- **Security**: No high/critical vulnerabilities
- **Type Safety**: Zero TypeScript errors

## Debugging Tests

### Common Issues

1. **Firebase Initialization Errors**

    ```bash
    # Check environment variables
    echo $FIREBASE_PRIVATE_KEY

    # Verify Firebase project configuration
    firebase projects:list
    ```

2. **Service Worker Registration Failures**

    ```javascript
    // Check service worker in browser dev tools
    navigator.serviceWorker.getRegistrations().then(console.log);
    ```

3. **Token Generation Issues**
    ```javascript
    // Test token generation manually
    import { getToken } from "firebase/messaging";
    getToken(messaging, { vapidKey: "your-vapid-key" }).then(console.log);
    ```

### Debug Logging

```typescript
// Enable debug logging
process.env.LOG_LEVEL = "debug";
localStorage.setItem("firebase-debug", "true");

// Custom debug utilities
export const debugNotification = (payload) => {
    if (process.env.NODE_ENV === "test") {
        console.log("Debug Notification:", JSON.stringify(payload, null, 2));
    }
};
```

## Best Practices

### Test Organization

- Group related tests in describe blocks
- Use descriptive test names
- Follow AAA pattern (Arrange, Act, Assert)
- Keep tests independent and isolated

### Mock Management

- Reset mocks between tests
- Use realistic mock data
- Mock external dependencies consistently
- Avoid over-mocking

### Performance

- Use `beforeEach` and `afterEach` for setup/cleanup
- Avoid unnecessary async operations
- Use test timeouts appropriately
- Clean up resources after tests

### Maintenance

- Update tests when code changes
- Remove obsolete tests
- Keep test utilities up to date
- Document complex test scenarios

## Troubleshooting

### Test Failures

1. **Check Environment Setup**
    - Verify environment variables
    - Ensure dependencies are installed
    - Check database connectivity

2. **Review Test Logs**
    - Look for specific error messages
    - Check stack traces
    - Verify mock configurations

3. **Isolate Issues**
    - Run individual test files
    - Use `test.only` for debugging
    - Add debug logging

### Performance Issues

1. **Slow Tests**
    - Profile test execution
    - Optimize database operations
    - Reduce unnecessary async operations

2. **Memory Leaks**
    - Check for unclosed connections
    - Verify cleanup in `afterEach`
    - Use memory profiling tools

## Resources

- [Vitest Documentation](https://vitest.dev/)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Firebase Testing Guide](https://firebase.google.com/docs/emulator-suite)
- [Jest Mocking Guide](https://jestjs.io/docs/mock-functions)

## Contributing

When adding new tests:

1. Follow existing patterns and conventions
2. Add appropriate documentation
3. Ensure tests are reliable and fast
4. Update this guide if needed
5. Run full test suite before submitting
